('C:\\Users\\<USER>\\Desktop\\Linkedin-Post-Crawler\\linkedin-employee-crawler\\build\\LinkedInEmployeeScraper\\PYZ-00.pyz',
 [('IPython',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.ast_mod',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\ast_mod.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_emscripten',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\_process_emscripten.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('OpenSSL',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\ast.py', 'PYMODULE'),
  ('asttokens',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\base64.py',
   'PYMODULE'),
  ('bdb', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\bdb.py', 'PYMODULE'),
  ('bisect',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\bisect.py',
   'PYMODULE'),
  ('brotli',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bz2', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\bz2.py', 'PYMODULE'),
  ('cProfile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\cProfile.py',
   'PYMODULE'),
  ('calendar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('chunk', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\chunk.py', 'PYMODULE'),
  ('cmd', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\code.py', 'PYMODULE'),
  ('codeop',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('cycler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\decimal.py',
   'PYMODULE'),
  ('decorator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('difflib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\difflib.py',
   'PYMODULE'),
  ('dis', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\doctest.py',
   'PYMODULE'),
  ('dotenv',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\email\\utils.py',
   'PYMODULE'),
  ('exceptiongroup',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('executing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing._pytest_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\executing\\_pytest_utils.py',
   'PYMODULE'),
  ('executing.executing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('filecmp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\filecmp.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\gettext.py',
   'PYMODULE'),
  ('glob', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\hmac.py', 'PYMODULE'),
  ('html',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\ipaddress.py',
   'PYMODULE'),
  ('jedi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\__init__.py',
   'PYMODULE'),
  ('jedi._compatibility',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('linkedin_employee',
   'C:\\Users\\<USER>\\Desktop\\Linkedin-Post-Crawler\\linkedin-employee-crawler\\linkedin_employee.py',
   'PYMODULE'),
  ('logging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\lzma.py', 'PYMODULE'),
  ('main_page',
   'C:\\Users\\<USER>\\Desktop\\Linkedin-Post-Crawler\\linkedin-employee-crawler\\main_page.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib_inline',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib_inline\\__init__.py',
   'PYMODULE'),
  ('matplotlib_inline.backend_inline',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib_inline\\backend_inline.py',
   'PYMODULE'),
  ('matplotlib_inline.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\matplotlib_inline\\config.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('msilib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\msilib\\__init__.py',
   'PYMODULE'),
  ('msilib.schema',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\msilib\\schema.py',
   'PYMODULE'),
  ('msilib.sequence',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\msilib\\sequence.py',
   'PYMODULE'),
  ('msilib.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\msilib\\text.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\optparse.py',
   'PYMODULE'),
  ('outcome',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('parso',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\pdb.py', 'PYMODULE'),
  ('pickle',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pprint.py',
   'PYMODULE'),
  ('profile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\profile.py',
   'PYMODULE'),
  ('prompt_toolkit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pstats.py',
   'PYMODULE'),
  ('psutil',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psycopg2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('pure_eval',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('queue', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\queue.py', 'PYMODULE'),
  ('quopri',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\random.py',
   'PYMODULE'),
  ('readline',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('requests',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\runpy.py', 'PYMODULE'),
  ('secrets',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\selectors.py',
   'PYMODULE'),
  ('selenium',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_msi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_msi.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_wininst',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_wininst.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\shlex.py', 'PYMODULE'),
  ('shutil',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\signal.py',
   'PYMODULE'),
  ('site', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\site.py', 'PYMODULE'),
  ('six',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\smtplib.py',
   'PYMODULE'),
  ('sniffio',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('sortedcontainers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\ssl.py', 'PYMODULE'),
  ('stack_data',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('statistics',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\threading.py',
   'PYMODULE'),
  ('timeit',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\timeit.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('trio',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\tty.py', 'PYMODULE'),
  ('typing',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\uuid.py', 'PYMODULE'),
  ('wave', 'C:\\laragon\\bin\\python\\python-3.10\\lib\\wave.py', 'PYMODULE'),
  ('wcwidth',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\webbrowser.py',
   'PYMODULE'),
  ('webdriver_manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('websocket',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('xml',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zstandard',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'C:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
