# linkedin_employee.py
import os, time, pickle, threading, random, re, signal
from queue import Queue
from typing import Callable, Optional, List, Tuple

import psycopg2
from psycopg2 import OperationalError
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from concurrent.futures import ThreadPoolExecutor, as_completed

# =========================
# CONFIG (env-first)
# =========================
load_dotenv()

DEFAULT_DB_URL = os.getenv("DB_URL") or "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
DEFAULT_TABLE = os.getenv("TABLE_NAME", "linkedin_companies_data")
DEFAULT_COOKIES = os.getenv("COOKIES_FILE", "cookies.pkl")
DEFAULT_LOG_FILE = os.getenv("LOG_FILE", "debug_log.txt")

DEFAULT_CLAIM_LIMIT = int(os.getenv("CLAIM_LIMIT", "40"))

RESTART_ROWS = int(os.getenv("RESTART_ROWS", "300"))
RESTART_SECS = int(os.getenv("RESTART_SECS", str(20 * 60)))

MIN_DELAY = float(os.getenv("MIN_DELAY", "1.5"))
MAX_DELAY = float(os.getenv("MAX_DELAY", "3.5"))

# multiple xpath fallbacks
XPATHS = [
    "//div[contains(@class,'org-top-card-summary-info-list')]//a/span",
    "//dt[contains(.,'Company size')]/following-sibling::dd[1]",
    "//*[contains(translate(.,'EMPLOYADOSKARYAWN','empoyadoskaryawn'),'employee')][1]"
]

EMP_RX = re.compile(
    r'(?i)(?P<range>\d[\d,.\u2009\u00A0]*\s*[kK]?\s*[–-]\s*\d[\d,.\u2009\u00A0]*\s*[kK]?)\s*employees'
    r'|(?P<simple>\d[\d,.\u2009\u00A0]*\s*[kK]?\+?)\s*employees'
)

# a simple global gate to avoid hammering LI too fast across threads
_global_gate = threading.Semaphore(1)


class LinkedInEmployeeScraper:
    """
    Usable class wrapper around the LinkedIn employee count scraper.
    - call start(workers) to begin
    - call stop() to request graceful shutdown
    - pass a log handler function to stream logs into your UI
    """

    def __init__(
        self,
        db_url: Optional[str] = None,
        table_name: str = DEFAULT_TABLE,
        cookies_file: str = DEFAULT_COOKIES,
        claim_limit: int = DEFAULT_CLAIM_LIMIT,
        log_file: str = DEFAULT_LOG_FILE,
        log_handler: Optional[Callable[[str], None]] = None
    ):
        self.db_url = db_url or DEFAULT_DB_URL
        self.table_name = table_name
        self.cookies_file = cookies_file
        self.claim_limit = claim_limit
        self.log_file = log_file

        self._shutdown = threading.Event()
        self._executor: Optional[ThreadPoolExecutor] = None
        self._workers = 0
        self._log_handler = log_handler
        self._print_lock = threading.Lock()

        # compute driver path lazily in setup_driver, to keep init fast
        self._driver_path = None

    # ------------- public api -------------
    def start(self, workers: int):
        if self._executor is not None:
            self._log("already running; stop first before starting again")
            return
        self._workers = max(1, workers)
        start = time.time()
        self._log(f"Starting {self._workers} workers…")

        self._executor = ThreadPoolExecutor(max_workers=self._workers)
        self._futs = [self._executor.submit(self._worker_main, i + 1) for i in range(self._workers)]

        # detach completion watcher in a thread so caller isn't blocked
        watcher = threading.Thread(target=self._watch_futures, args=(start,), daemon=True)
        watcher.start()

    def stop(self):
        self._log("Received stop request. Shutting down workers…")
        self._shutdown.set()

    def is_running(self) -> bool:
        return self._executor is not None

    def set_log_handler(self, handler: Callable[[str], None]):
        self._log_handler = handler

    # ------------- internals -------------
    def _watch_futures(self, start_ts: float):
        try:
            for _ in as_completed(self._futs):
                if self._shutdown.is_set():
                    break
        except Exception as e:
            self._log(f"[watcher] {e}")
        finally:
            if self._executor:
                self._executor.shutdown(wait=False, cancel_futures=True)
            self._executor = None
            elapsed = time.time() - start_ts
            self._log(f"✅ All done in {int(elapsed)}s.")

    def _log(self, msg: str, wid: Optional[int] = None):
        prefix = f"[W{wid}] " if wid is not None else ""
        line = f"{time.strftime('[%Y-%m-%d %H:%M:%S]')} {prefix}{msg}"
        with self._print_lock:
            # to UI
            if self._log_handler:
                self._log_handler(line)
            else:
                print(line, flush=True)
            # to file
            try:
                with open(self.log_file, "a", encoding="utf-8") as f:
                    f.write(line + "\n")
            except Exception:
                pass

    # ---------------- DB helpers ----------------
    def _connect_db(self, wid=None):
        while not self._shutdown.is_set():
            try:
                conn = psycopg2.connect(
                    self.db_url,
                    keepalives=1, keepalives_idle=30, keepalives_interval=10, keepalives_count=5,
                )
                conn.autocommit = False
                cur = conn.cursor()
                cur.execute("SET SESSION statement_timeout = 30000")
                cur.execute("SET SESSION idle_in_transaction_session_timeout = 30000")
                self._log("✅ Connected to PostgreSQL.", wid)
                return conn, cur
            except OperationalError as e:
                self._log(f"[DB ERROR] Connection failed: {e}. Retrying in 5s...", wid)
                time.sleep(5)
        raise RuntimeError("shutdown while connecting to DB")

    def _ensure_db(self, conn, cur, wid=None):
        try:
            cur.execute("SELECT 1")
        except (psycopg2.InterfaceError, psycopg2.OperationalError):
            self._log("🔄 Reconnecting to PostgreSQL...", wid)
            return self._connect_db(wid)
        return conn, cur

    def _claim_batch(self, conn, cur, limit, wid=None) -> List[Tuple[str, str]]:
        conn, cur = self._ensure_db(conn, cur, wid)
        try:
            cur.execute(f"""
                WITH to_claim AS (
                    SELECT universal_name, linkedin_url
                    FROM {self.table_name}
                    WHERE employee_count IS NULL
                      AND (linkedin_url IS NOT NULL AND linkedin_url <> '')
                    ORDER BY universal_name
                    FOR UPDATE SKIP LOCKED
                    LIMIT %s
                )
                UPDATE {self.table_name} AS t
                   SET employee_count = '__IN_PROGRESS__'
                  FROM to_claim c
                 WHERE t.universal_name = c.universal_name
                RETURNING t.universal_name, t.linkedin_url;
            """, (limit,))
            rows = cur.fetchall()
            conn.commit()
            return rows
        except Exception as e:
            conn.rollback()
            self._log(f"[ERROR] claim_batch failed => {e}", wid)
            time.sleep(2)
            return []

    def _update_one(self, conn, cur, name, value, wid=None):
        sql = f"""UPDATE {self.table_name}
                     SET employee_count = %s
                   WHERE universal_name = %s
                     AND employee_count = '__IN_PROGRESS__'"""
        for attempt in range(2):
            try:
                cur.execute(sql, (value, name))
                conn.commit()
                if cur.rowcount == 0 and attempt == 0:
                    conn, cur = self._ensure_db(conn, cur, wid)
                    continue
                self._log(f"📝 {name} -> {value}", wid)
                return conn, cur
            except (psycopg2.InterfaceError, psycopg2.OperationalError):
                self._log("🔄 Update failed; reconnecting...", wid)
                conn, cur = self._connect_db(wid)
        return conn, cur

    def _mark_empty_url(self, conn, cur, name, wid=None):
        try:
            cur.execute(
                f"UPDATE {self.table_name} SET employee_count = %s "
                f"WHERE universal_name = %s AND employee_count='__IN_PROGRESS__'",
                ('-1', name)
            )
            conn.commit()
            self._log(f"✅ {name} -> -1 (empty URL)", wid)
        except Exception as e:
            conn.rollback()
            self._log(f"[ERROR] DB update failed for {name} => {e}", wid)
        return conn, cur

    # ---------------- Selenium ----------------
    def _setup_driver(self, wid=None):
        if self._driver_path is None:
            self._driver_path = ChromeDriverManager().install()

        options = Options()
        options.add_argument("--headless=new")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        options.page_load_strategy = "eager"

        driver = webdriver.Chrome(service=Service(self._driver_path), options=options)
        driver.set_window_size(1366, 900)

        driver.get("https://www.linkedin.com/")
        try:
            with open(self.cookies_file, "rb") as f:
                for c in pickle.load(f):
                    try:
                        driver.add_cookie(c)
                    except Exception:
                        pass
        except FileNotFoundError:
            self._log("[COOKIE WARN] cookies.pkl not found; you may not be logged in.", wid)

        driver.get("https://www.linkedin.com/feed/")
        WebDriverWait(driver, 12).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        # refresh/save cookies to extend session
        try:
            with open(self.cookies_file, "wb") as f:
                pickle.dump(driver.get_cookies(), f)
        except Exception:
            pass

        driver.execute_script("window.scrollTo(0, 600)")
        return driver

    # ---------------- parsing ----------------
    @staticmethod
    def _to_int_k(s: str) -> str:
        s2 = s.lower().replace(',', '').replace('.', '')
        if s2.endswith('k'):
            base = s2[:-1]
            if base.isdigit():
                return str(int(base) * 1000)
        return s2

    def _normalize_count_text(self, txt: str) -> Optional[str]:
        t = txt.replace('\u2009', ' ').replace('\u00A0', ' ')
        m = EMP_RX.search(t)
        if not m:
            return None
        s = (m.group('range') or m.group('simple') or '').strip()
        s = s.replace('–', '-').replace(' ', '')
        if '-' in s:
            a, b = s.split('-', 1)
            a = self._to_int_k(a)
            b = self._to_int_k(b.rstrip('+'))
            if a.isdigit() and b.isdigit():
                return f"{a}-{b}"
        if s.lower().endswith('k+') or s.lower().endswith('k'):
            n = s.lower().rstrip('+').rstrip('k')
            if n.isdigit():
                return f"{int(n) * 1000}+"
        if s.endswith('+') and s[:-1].isdigit():
            return s
        if s.isdigit():
            return f"{s}+"
        return s

    def _extract_employee_count(self, driver, url, wid=None) -> Optional[str]:
        for attempt in range(2):
            try:
                with _global_gate:
                    # simple global throttle
                    time.sleep(0.8 + random.random() * 0.8)
                    if attempt == 0:
                        driver.get(url)
                    else:
                        driver.refresh()

                # try multiple XPaths
                for xp in XPATHS:
                    els = driver.find_elements(By.XPATH, xp)
                    for el in els:
                        t = el.text.strip()
                        norm = self._normalize_count_text(t)
                        if norm:
                            self._log(f"[PARSE] {t} -> {norm}", wid)
                            return norm
            except Exception as e:
                self._log(f"[RETRY {attempt}] {url} => {e}", wid)
        return None

    # ---------------- worker loop ----------------
    def _worker_main(self, wid: int):
        self._log("🚀 Worker starting", wid)
        conn, cur = self._connect_db(wid)
        driver = self._setup_driver(wid)

        last_restart_t = time.monotonic()
        rows_since_restart = 0

        try:
            while not self._shutdown.is_set():
                rows = self._claim_batch(conn, cur, self.claim_limit, wid)
                if not rows:
                    self._log("🎉 No more rows to claim. Worker exiting.", wid)
                    break

                self._log(f"📦 Claimed {len(rows)} rows", wid)

                for name, url in rows:
                    if self._shutdown.is_set():
                        break
                    conn, cur = self._ensure_db(conn, cur, wid)

                    if not url:
                        self._log(f"❌ Skipped {name}: Empty URL", wid)
                        conn, cur = self._mark_empty_url(conn, cur, name, wid)
                        continue

                    self._log(f"➡️  {name} - {url}", wid)
                    count = self._extract_employee_count(driver, url, wid)
                    to_store = count if count is not None else '-1'
                    if count is None:
                        self._log(f"⚠️  Not found for {name}, setting -1", wid)

                    conn, cur = self._update_one(conn, cur, name, to_store, wid)

                    rows_since_restart += 1
                    time.sleep(MIN_DELAY + random.random() * (MAX_DELAY - MIN_DELAY))

                    if rows_since_restart >= RESTART_ROWS or (time.monotonic() - last_restart_t) >= RESTART_SECS:
                        self._log("♻️ Recycling Selenium driver...", wid)
                        try:
                            driver.quit()
                        except Exception:
                            pass
                        time.sleep(2 + random.random())
                        driver = self._setup_driver(wid)
                        last_restart_t = time.monotonic()
                        rows_since_restart = 0
        finally:
            try:
                driver.quit()
            except Exception:
                pass
            try:
                cur.close()
                conn.close()
            except Exception:
                pass
            self._log("🛑 Worker stopped", wid)
