# main_page.py
import tkinter as tk
from tkinter import ttk, messagebox
from queue import Queue, Empty
import threading
import time

from linkedin_employee import LinkedInEmployeeScraper

class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("LinkedIn Employee Scraper")
        self.geometry("900x600")

        # top frame: controls
        top = ttk.Frame(self, padding=10)
        top.pack(side=tk.TOP, fill=tk.X)

        ttk.Label(top, text="Workers:").pack(side=tk.LEFT)
        self.workers_var = tk.IntVar(value=5)
        self.workers_spin = ttk.Spinbox(top, from_=1, to=50, width=5, textvariable=self.workers_var)
        self.workers_spin.pack(side=tk.LEFT, padx=8)

        self.start_btn = ttk.Button(top, text="Start", command=self.on_start)
        self.start_btn.pack(side=tk.LEFT, padx=4)

        self.stop_btn = ttk.Button(top, text="Stop", command=self.on_stop, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=4)

        # status label
        self.status_var = tk.StringVar(value="idle")
        ttk.Label(top, textvariable=self.status_var).pack(side=tk.LEFT, padx=12)

        # log box
        log_frame = ttk.Frame(self, padding=10)
        log_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_frame, wrap="word", state=tk.DISABLED)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scroll = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scroll.set)

        # log queue
        self.log_queue = Queue()

        # scraper instance
        self.scraper = LinkedInEmployeeScraper(log_handler=self._enqueue_log)

        # periodic UI update of logs
        self.after(100, self._drain_logs)

        # ensure clean stop on close
        self.protocol("WM_DELETE_WINDOW", self.on_close)

    def _enqueue_log(self, line: str):
        self.log_queue.put(line)

    def _append_log(self, line: str):
        self.log_text.configure(state=tk.NORMAL)
        self.log_text.insert(tk.END, line + "\n")
        self.log_text.see(tk.END)
        self.log_text.configure(state=tk.DISABLED)

    def _drain_logs(self):
        try:
            while True:
                line = self.log_queue.get_nowait()
                self._append_log(line)
        except Empty:
            pass
        self.after(100, self._drain_logs)

    def on_start(self):
        if self.scraper.is_running():
            messagebox.showinfo("Info", "Scraper already running.")
            return
        workers = max(1, int(self.workers_var.get()))
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.status_var.set(f"running ({workers} workers)")
        # run scraper start on another thread so UI doesn't freeze
        threading.Thread(target=self.scraper.start, args=(workers,), daemon=True).start()

    def on_stop(self):
        if self.scraper.is_running():
            self.scraper.stop()
            self.status_var.set("stopping…")
        else:
            self.status_var.set("idle")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

    def on_close(self):
        try:
            if self.scraper.is_running():
                self.scraper.stop()
                self.status_var.set("stopping…")
                # small grace period to flush logs
                self.after(500, self.destroy)
            else:
                self.destroy()
        except Exception:
            self.destroy()

def run_ui():
    app = MainApp()
    app.mainloop()

if __name__ == "__main__":
    run_ui()
