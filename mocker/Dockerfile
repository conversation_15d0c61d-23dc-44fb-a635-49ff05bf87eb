# Use a lightweight Python base image
FROM python:3.9-slim

# Set the working directory in the container
WORKDIR /app

# Copy the required files into the container
COPY linkedin_scraper_api.py .
COPY linkedin_post_url.py .
COPY shann5.pkl .
COPY LinkedIn_URLs.txt .
COPY requirements.txt .
COPY chromedriver .
COPY processed_posts.txt .
COPY profile_urls.txt .
COPY urls.txt .
COPY processed_urns/ processed_urns/ 

# Install system dependencies for Selenium and Chrome
RUN apt-get update && \
    apt-get install -y wget unzip curl chromium && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Ensure the processed_urns directory exists inside the container
RUN mkdir -p processed_urns

# Set environment variables (optional: replace sensitive data with actual secrets or use runtime variables)
ENV PG_CONNECTION_STRING="postgresql://beyz-web_owner:<EMAIL>/beyz-dev?sslmode=require"
ENV EMAIL_API_KEY="uRRSTeKLdlQAiQGBCfp0Cu5u1"

# Expose ports if needed (e.g., for monitoring or debugging)
EXPOSE 8000

# Run the Python script as the default command
CMD ["python", "linkedin_scraper_api.py"]
