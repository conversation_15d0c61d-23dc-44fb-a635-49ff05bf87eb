# improved linkedin_scraper_api.py plan

# the code logic ( first stage )
# user input one of the company url
# the code will navigate to the company url
# the code will click the show all button for all the related company
# the code get all the related company ( the total company will be set by user e.g. 50 company )
# the code will click one of the company and do the same until the total company set by user is reached

# the code logic ( second stage )
# after getting the company the code will navigate to the people page from each company
# the code will extract people with followers more than 5k
# the code will save the profile url for the linkedin_scraper_api.py to process

# show all related company list
# $x("//button[contains(@aria-label,'Show all similar pages')]")
# more company list
# $x("//h3[contains(normalize-space(.), 'Pages people also viewed')]/parent::header/following-sibling::div[contains(@class, 'cards-group__list')]")
# more company list links
# $x("//h3[contains(normalize-space(.), 'Pages people also viewed')]/parent::header/following-sibling::div[contains(@class, 'cards-group__list')]//div[contains(@data-view-name,'premium-pages-browsemap')]//div[contains(@class,'display-flex')]//a")
# more company list followers ( always take the second one )
# $x("//h3[contains(normalize-space(.), 'Pages people also viewed')]/parent::header/following-sibling::div[contains(@class, 'cards-group__list')]//div[contains(@data-view-name,'premium-pages-browsemap')]//span")

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import os, time, pickle

# ======== LOGIN LOGIC (REUSED) ========

def initialize_driver():
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    # Optional: Headless if you want
    # chrome_options.add_argument("--headless")
    driver_path = os.path.join(os.getcwd(), "chromedriver.exe")
    return webdriver.Chrome(service=Service(driver_path), options=chrome_options)

def login_to_linkedin(driver, cookies_file="shann5.pkl"):
    driver.get("https://www.linkedin.com/login")
    if os.path.exists(cookies_file):
        with open(cookies_file, "rb") as f:
            cookies = pickle.load(f)
        for cookie in cookies:
            driver.add_cookie(cookie)
        driver.refresh()
        time.sleep(3)
        if "feed" in driver.current_url:
            print("✅ Logged in using cookies.")
            return True

    print("🔐 Manual login required. Please login.")
    WebDriverWait(driver, 60).until(EC.url_contains("feed"))
    with open(cookies_file, "wb") as f:
        pickle.dump(driver.get_cookies(), f)
    return True

# ======== COMPANY CRAWLING LOGIC ========

visited = set()
company_urls = []

def crawl_related_companies(driver, url, max_count):
    global visited, company_urls
    if len(company_urls) >= max_count or url in visited:
        return
    visited.add(url)

    print(f"🌐 Visiting: {url}")
    driver.get(url)
    time.sleep(3)

    # Try clicking "See all" related companies (adjust based on actual LinkedIn layout)
    try:
        show_all_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "//a[contains(text(),'Show all')]"))
        )
        show_all_button.click()
        print("🔎 Clicked Show all")
        time.sleep(3)
    except:
        print("❌ Show all button not found or already expanded.")

    # Collect related company links
    related_links = driver.find_elements(By.XPATH, "//a[contains(@href, '/company/')]")
    for link in related_links:
        href = link.get_attribute("href")
        if href and href not in visited and len(company_urls) < max_count:
            company_urls.append(href)

    print(f"📦 Collected {len(company_urls)} companies so far.")

    # Recursively visit the next company in the list
    while len(company_urls) > len(visited) and len(company_urls) < max_count:
        next_url = company_urls[len(visited)]
        crawl_related_companies(driver, next_url, max_count)

# ======== MAIN DRIVER ========

def main():
    driver = initialize_driver()
    login_to_linkedin(driver)

    start_url = input("Enter LinkedIn company URL to start from: ").strip()
    max_companies = int(input("Enter total number of companies to collect: ").strip())

    crawl_related_companies(driver, start_url, max_companies)

    # Save collected URLs
    with open("collected_companies.txt", "w") as f:
        for url in company_urls:
            f.write(url + "\n")

    print(f"✅ Finished. Collected {len(company_urls)} company URLs.")
    driver.quit()

if __name__ == "__main__":
    main()
