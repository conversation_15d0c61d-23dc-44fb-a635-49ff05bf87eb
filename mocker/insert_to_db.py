import psycopg2
import time
import re
from psycopg2.extras import execute_values  # Import execute_values properly

# Database connection string
DB_CONNECTION_STRING = "postgresql://beyz-dev:<EMAIL>/beyz-production?sslmode=require"

# Log file path
DEBUG_LOG_FILE = "debug_log.txt"

def connect_to_database():
    """Establishes a database connection with retries."""
    retries = 5
    for attempt in range(retries):
        try:
            conn = psycopg2.connect(DB_CONNECTION_STRING)
            conn.autocommit = True
            print("[INFO] Database connected.")
            return conn
        except Exception as e:
            print(f"[ERROR] Database connection failed: {e}. Retrying {attempt+1}/{retries}...")
            time.sleep(3)  # Wait before retrying
    return None

def extract_failed_emails():
    """Extracts all failed emails from the debug log."""
    failed_emails = set()  # Use a set to avoid duplicates
    with open(DEBUG_LOG_FILE, "r", encoding="utf-8") as f:
        log_lines = f.readlines()
    
    for line in log_lines:
        # Match emails in lines containing "Extracted email"
        email_match = re.search(r"Email ([\w\.-]+@[\w\.-]+\.\w+) passed validation", line)
        if email_match:
            failed_emails.add(email_match.group(1))

    print(f"[INFO] Extracted {len(failed_emails)} unique emails that failed insertion.")
    return list(failed_emails)

def insert_emails_into_db(emails):
    """Inserts emails into the database, handling reconnections."""
    if not emails:
        print("[INFO] No emails to insert.")
        return
    
    conn = connect_to_database()
    if not conn:
        print("[ERROR] Could not connect to database. Exiting...")
        return
    
    retries = 3
    for attempt in range(retries):
        try:
            cursor = conn.cursor()
            query = 'INSERT INTO "mailingList" (email) VALUES %s ON CONFLICT (email) DO NOTHING'
            execute_values(cursor, query, [(email,) for email in emails])  # Correct way to use execute_values
            print(f"[SUCCESS] Inserted {len(emails)} emails into the database.")
            cursor.close()
            conn.close()
            return
        except psycopg2.OperationalError:
            print(f"[ERROR] Database connection lost. Attempt {attempt+1}/{retries}. Reconnecting...")
            conn = connect_to_database()
            if not conn:
                print("[ERROR] Could not reconnect. Skipping batch.")
                return
            time.sleep(2)  # Small delay before retrying
        except Exception as e:
            print(f"[ERROR] Unexpected error: {e}")
            return
    
    print("[ERROR] Max retries reached. Emails not inserted.")

# Run the process
emails_to_insert = extract_failed_emails()
insert_emails_into_db(emails_to_insert)
