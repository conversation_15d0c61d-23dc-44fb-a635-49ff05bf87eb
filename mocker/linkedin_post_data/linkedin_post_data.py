import os
import time
import pickle
import pyperclip
from datetime import datetime
from openpyxl import Workbook
from openpyxl import load_workbook
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC

# === CONFIGURATION ===
PROFILE_URLS_FILE = "profile_urls.txt"
DEBUG_FILE = "debug_log.txt"
COOKIES_FILE = "shann5.pkl"

def log_debug(message):
    with open(DEBUG_FILE, "a", encoding="utf-8") as f:
        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")

def reset_debug_log():
    open(DEBUG_FILE, "w").close()

def load_profile_urls():
    if not os.path.exists(PROFILE_URLS_FILE):
        return []
    with open(PROFILE_URLS_FILE, "r", encoding="utf-8") as f:
        return [line.strip() for line in f.readlines() if line.strip()]

def initialize_driver():
    options = webdriver.ChromeOptions()
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    return webdriver.Chrome(service=Service(), options=options)

def login_to_linkedin(driver):
    driver.get("https://www.linkedin.com/login")
    if os.path.exists(COOKIES_FILE):
        with open(COOKIES_FILE, "rb") as f:
            cookies = pickle.load(f)
        for cookie in cookies:
            driver.add_cookie(cookie)
        driver.refresh()
        time.sleep(3)
        if "feed" in driver.current_url:
            log_debug("Logged in using cookies.")
            return True

    WebDriverWait(driver, 60).until(EC.url_contains("feed"))
    with open(COOKIES_FILE, "wb") as f:
        pickle.dump(driver.get_cookies(), f)
    return True

def slow_scroll(driver, pause=1, steps=500, max_attempts=100):
    attempts = 0
    last_height = driver.execute_script("return document.body.scrollHeight")
    while attempts < max_attempts:
        driver.execute_script(f"window.scrollBy(0, {steps});")
        time.sleep(pause)
        new_height = driver.execute_script("return document.body.scrollHeight")
        if new_height == last_height:
            attempts += 1
        else:
            attempts = 0
        last_height = new_height

def extract_post_data(driver, post):
    data = {
        "link": None,
        "date": None,
        "tags": [],
        "likes": None,
        "reposts": None,
        "comments": None,
        "content": None
    }

    log_debug("Extracting post data...")

    try:
        urn = post.get_attribute("data-urn")
        activity_id = urn.split(":")[-1]
        post_url = f"https://www.linkedin.com/feed/update/urn:li:activity:{activity_id}"
        data["link"] = post_url
        log_debug(f"Post link constructed from URN: {post_url}")
    except Exception as e:
        log_debug(f"[ERROR] Failed to construct post link from URN: {e}")


    try:
        log_debug("Extracting post date...")
        date = post.find_element(By.XPATH, ".//div[contains(@class,'actor__meta')]/span//span[@aria-hidden='true'][1]")
        data["date"] = date.text
        log_debug(f"Date: {data['date']}")
    except Exception as e:
        log_debug(f"[WARN] Could not extract date: {e}")

    try:
        see_more_btn = post.find_element(By.XPATH, ".//button[contains(@class, 'see-more') and contains(@class, 'feed-shared-inline-show-more-text')]")
        if see_more_btn and see_more_btn.is_displayed():
            driver.execute_script("arguments[0].click();", see_more_btn)
            log_debug("Clicked '...see more' to expand post content.")
            time.sleep(0.5)  # Small pause to allow content to expand
    except Exception as e:
        log_debug(f"[INFO] No 'see more' button found or click failed: {e}")

    try:
        log_debug("Extracting hashtags...")
        tag_elements = post.find_elements(By.XPATH, ".//a[contains(@href, '/feed/hashtag')]")
        data["tags"] = []

        for tag in tag_elements:
            raw = tag.text.strip()
            if raw:
                lines = raw.split('\n')
                last_line = lines[-1].replace('#', '').strip()
                if last_line:
                    data["tags"].append(last_line)

        log_debug(f"Tags: {data['tags']}")
    except Exception as e:
        log_debug(f"[WARN] Could not extract tags: {e}")




    try:
        log_debug("Extracting likes...")
        likes = post.find_element(By.XPATH, ".//span[contains(@class,'social-details-social-counts__reactions-count')]")
        data["likes"] = likes.text
        log_debug(f"Likes: {data['likes']}")
    except Exception as e:
        log_debug(f"[WARN] Could not extract likes: {e}")

    try:
        log_debug("Extracting reposts...")
        reposts = post.find_element(By.XPATH, ".//button[contains(@aria-label, 'repost')]/span")
        data["reposts"] = reposts.text
        log_debug(f"Reposts: {data['reposts']}")
    except Exception as e:
        log_debug(f"[WARN] Could not extract reposts: {e}")

    try:
        log_debug("Extracting comments count...")
        comments = post.find_element(By.XPATH, ".//button[contains(@aria-label, 'comment')]/span")
        data["comments"] = comments.text
        log_debug(f"Comments: {data['comments']}")
    except Exception as e:
        log_debug(f"[WARN] Could not extract comments: {e}")

    try:
        log_debug("Extracting post content...")
        contents = post.find_elements(By.XPATH, ".//span[contains(@class,'break-words')]//span[contains(@dir, 'ltr')]")
        data["content"] = "\n".join([c.text for c in contents])
        log_debug(f"Content: {data['content'][:100]}...")  # log only the first 100 chars
    except Exception as e:
        log_debug(f"[WARN] Could not extract content: {e}")

    log_debug("Finished extracting post data.\n")
    return data


def save_to_excel(posts):
    filename = datetime.now().strftime("%Y-%m-%d") + "_post_data.xlsx"
    wb = Workbook()
    ws = wb.active
    ws.title = "Posts"
    ws.append(["link", "date", "tags", "likes", "reposts", "comments", "content"])
    for p in posts:
        ws.append([
            p["link"],
            p["date"],
            ", ".join(p["tags"]),
            p["likes"],
            p["reposts"],
            p["comments"],
            p["content"]
        ])
    wb.save(filename)
    log_debug(f"[SAVED] Data saved to {filename}")

def init_excel():
    today = datetime.now().strftime("%Y-%m-%d")
    filename = f"{today}_post_data.xlsx"
    wb = Workbook()
    ws = wb.active
    ws.title = "Posts"
    ws.append(["link", "date", "tags", "likes", "reposts", "comments", "content"])
    wb.save(filename)
    return filename, wb, ws


def append_post_to_excel(filename, ws_title, post_data):
    try:
        log_debug(f"[DEBUG] Opening Excel file: {filename}")
        wb = load_workbook(filename)
        ws = wb[ws_title]

        log_debug(f"[DEBUG] Writing data: {post_data['link']}")
        ws.append([
            post_data["link"],
            post_data["date"],
            ", ".join(post_data["tags"]),
            post_data["likes"],
            post_data["reposts"],
            post_data["comments"],
            post_data["content"]
        ])
        wb.save(filename)
        log_debug(f"[APPENDED] Post saved: {post_data['link']}")
    except Exception as e:
        log_debug(f"[ERROR] Failed to write to Excel: {e}")


def main():
    reset_debug_log()
    log_debug("[START] LinkedIn post scraper started.")
    
    urls = load_profile_urls()
    log_debug(f"[INFO] Loaded {len(urls)} profile URLs.")

    if not urls:
        log_debug("[ERROR] No profile URLs found. Exiting.")
        return

    driver = initialize_driver()
    log_debug("[INFO] WebDriver initialized.")

    try:
        login_success = login_to_linkedin(driver)
        if login_success:
            log_debug("[INFO] Logged in successfully.")
        else:
            log_debug("[ERROR] Login failed.")
            return
    except Exception as e:
        log_debug(f"[ERROR] Login process threw an exception: {e}")
        return

    filename, wb, ws = init_excel()
    ws_title = ws.title

    for url in urls:
        log_debug(f"[NAVIGATING] Visiting profile: {url}")
        try:
            driver.get(url)
            time.sleep(4)
        except Exception as e:
            log_debug(f"[ERROR] Failed to load profile URL: {e}")
            continue

        seen_urns = set()
        scroll_attempts = 0
        max_scroll_attempts = 30

        while scroll_attempts < max_scroll_attempts:
            posts = driver.find_elements(By.XPATH, "//div[@data-urn]")
            new_processed = False

            for post in posts:
                try:
                    urn = post.get_attribute("data-urn")
                    if not urn or urn in seen_urns:
                        continue
                    seen_urns.add(urn)

                    log_debug(f"[PROCESSING] New post found (URN: {urn})")
                    data = extract_post_data(driver, post)
                    if data["link"]:
                        append_post_to_excel(filename, ws_title, data)
                        new_processed = True
                    else:
                        log_debug("[WARN] Skipped post with no link.")
                except Exception as e:
                    log_debug(f"[ERROR] Failed extracting post: {e}")

            driver.execute_script("window.scrollBy(0, 700);")
            time.sleep(2)

            if not new_processed:
                scroll_attempts += 1
                log_debug(f"[INFO] No new posts found. Scroll attempt {scroll_attempts}/{max_scroll_attempts}.")
            else:
                scroll_attempts = 0  # reset scroll attempts if new post was processed

        log_debug(f"[INFO] Finished scraping profile: {url}")

    driver.quit()
    log_debug("[FINISHED] Scraping session complete.")



if __name__ == "__main__":
    main()
