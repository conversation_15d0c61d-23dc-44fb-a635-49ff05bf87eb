import os
import pickle
import re
import requests
import pandas as pd
import time
import psycopg2
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from datetime import datetime

class LinkedInPostURLScraper:
    def __init__(self, cookies_file="shann5.pkl", urls_file="LinkedIn_URLas.txt",csv_input_file="posts3.csv"):
        self.driver = None
        self.cookies_file = cookies_file
        self.urls_file = urls_file           
        self.log_callback = None
        self.csv_file = f"linkedin_comments_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.csv"
        self.connection_string = "postgresql://beyz-dev:<EMAIL>/beyz-production?sslmode=require"
        self.conn = psycopg2.connect(self.connection_string)
        self.cursor = self.conn.cursor()
        self.processed_emails = set()  # Set to track processed emails
        self.processed_posts_file = "processed_posts.txt"  # File to save processed posts
        self.csv_input_file = csv_input_file

        # Load previously processed posts if the file exists
        self.processed_posts = self.load_processed_posts()

    def connect_to_database(self):
        """Ensures the database connection is active"""
        try:
            self.conn = psycopg2.connect(self.connection_string)
            self.conn.autocommit = True  # Avoid transaction issues
        except Exception as e:
            self.log_debug(f"Database connection error: {e}")
            self.conn = None

    def ensure_connection(self):
        """Checks if the database connection is alive and reconnects if needed"""
        try:
            with self.conn.cursor() as cursor:
                cursor.execute("SELECT 1")
        except (psycopg2.OperationalError, psycopg2.InterfaceError):
            self.log_debug("Database connection lost. Reconnecting...")
            self.connect_to_database()

    def initialize_driver(self):
        """Initialize WebDriver using the local ChromeDriver executable."""
        self.log_debug("Initializing WebDriver...")
        if self.driver is None:
            chrome_driver_path = os.path.join(os.getcwd(), "chromedriver.exe")
            if not os.path.exists(chrome_driver_path):
                raise FileNotFoundError(f"ChromeDriver not found at {chrome_driver_path}")

            chrome_options = webdriver.ChromeOptions()

            chrome_options.add_argument("--start-maximized")
            # chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")

            self.driver = webdriver.Chrome(service=Service(chrome_driver_path), options=chrome_options)
            self.log_debug("WebDriver initialized using local ChromeDriver.")

    def log_debug(self, message):
        """Logs debug messages to both the console and a file, ensuring logs are saved immediately."""
        print(message)  # Print to console
        log_file = "debug_log.txt"  # File where logs will be stored

        try:
            with open(log_file, "a", encoding="utf-8") as f:  # Open in append mode
                f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")
        except Exception as e:
            print(f"Error writing to log file: {e}")


    def save_cookies(self):
        """Save cookies to the default file."""
        self.log_debug("Saving cookies...")
        with open(self.cookies_file, "wb") as f:
            pickle.dump(self.driver.get_cookies(), f)
        self.log_debug(f"Cookies saved to {self.cookies_file}")

    def load_cookies(self):
        """Prompt the user to load cookies."""
        self.log_debug("Loading cookies...")
        choice = "y"
        if choice == "y":
            if os.path.exists(self.cookies_file):
                with open(self.cookies_file, "rb") as f:
                    cookies = pickle.load(f)
                for cookie in cookies:
                    self.driver.add_cookie(cookie)
                self.log_debug("Cookies loaded from file.")
            else:
                self.log_debug("No cookies file found.")
        else:
            cookies = input("Paste cookies in JSON format: ").strip()
            try:
                cookies = eval(cookies)
                for cookie in cookies:
                    self.driver.add_cookie(cookie)
                self.log_debug("Cookies added manually.")
            except Exception as e:
                self.log_debug(f"Invalid cookies format: {e}")

    def is_valid_email(self, email):
        email_regex = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
        return re.match(email_regex, email)

    def validate_email_quality(self, email, api_key):
        """Validate the email quality using Million Verifier API."""
        self.log_debug(f"Validating email: {email}")
        url = f"https://api.millionverifier.com/api/v3/?api=uRRSTeKLdlQAiQGBCfp0Cu5u1&email={email}&timeout=10"

        # Avoid verifying emails that have already been processed
        if email in self.processed_emails:
            self.log_debug(f"Skipping email {email} as it has already been validated.")
            return True  # Consider it valid since it was already processed

        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_debug(f"API Response for {email}: {data}")

                # Check validity based on the 'result' field
                if data.get('result') == 'ok':
                    self.log_debug(f"Email {email} is valid.")
                    self.processed_emails.add(email)  # Mark the email as processed
                    return True
                else:
                    self.log_debug(f"Email {email} is invalid. Reason: {data.get('subresult', 'Unknown')}")
                    return False
            else:
                self.log_debug(f"API request failed with status code {response.status_code}: {response.text}")
                return False
        except Exception as e:
            self.log_debug(f"Error validating email {email}: {e}")
            return False

    def insert_into_database(self, name, title, linkedin_url, email):
        """Insert valid email and comment data into PostgreSQL."""
        self.ensure_connection()

        try:
            self.cursor = self.conn.cursor()  # reset cursor in case it's closed
            self.cursor.execute(
                'INSERT INTO "mailingList" (email, name, title, "linkedinURL") VALUES (%s, %s, %s, %s)',
                (email, name, title, linkedin_url)
            )
            self.conn.commit()
            self.log_debug(f"Inserted {email} into the database.")
        except Exception as e:
            self.log_debug(f"Error inserting {email} into the database: {e}")


    def login(self):
        self.log_debug("Starting LinkedIn login...")
        self.initialize_driver()
        self.driver.get("https://www.linkedin.com/login")
        try:
            self.load_cookies()
            self.driver.refresh()
            time.sleep(3)
            if "feed" in self.driver.current_url:
                self.log_debug("Logged in using cookies.")
                return True
        except Exception as e:
            self.log_debug(f"Error using cookies: {e}")

        WebDriverWait(self.driver, 60).until(EC.url_contains("feed"))
        self.log_debug("Manual login successful.")
        return True

    def load_processed_posts(self):
        """Load processed post indices from file."""
        if os.path.exists(self.processed_posts_file):
            with open(self.processed_posts_file, 'r') as file:
                lines = file.read().splitlines()
                return {int(line) for line in lines if line.isdigit()}  # Store as a set of integers
        return set()  # Default to an empty set if file is empty or missing

    def save_processed_posts(self, index):
        """Append a processed post index to the file."""
        with open(self.processed_posts_file, "a") as file:
            file.write(f"{index}\n")  # Append each new index instead of overwriting the file


    def scrape_post_comments_with_keywords(self):
        """Scrape posts and comments based on keywords from a .txt file."""
        self.log_debug("Starting to scrape posts based on keywords...")

        # Initialize the driver and login
        self.login()

        if os.path.exists(self.csv_input_file):
            try:
                with open(self.csv_input_file, "r", encoding="utf-8", errors="ignore") as f:
                    df = pd.read_csv(f, on_bad_lines='skip')  # skip bad rows
                if "Post" in df.columns:
                    urls = df["Post"].dropna().tolist()
                    self.log_debug(f"Loaded {len(urls)} post URLs from CSV file: {self.csv_input_file}")
                else:
                    self.log_debug(f"'Post' column not found in CSV file: {self.csv_input_file}")
            except Exception as e:
                self.log_debug(f"Error reading CSV file: {e}")

        # Check for keywords file
        elif os.path.exists(self.urls_file):
            self.log_debug(f"URLs file {self.urls_file} not found.")
            return
        else:
            self.log_debug("No valid input file found.")
            return

        if not urls:
            self.log_debug("No URLs found in the file.")
            return

        for url in urls:
            try:
                self.log_debug(f"Navigating to: {url}")
                self.driver.get(url)
                time.sleep(3)  # Wait for the page to load
                self.extract_posts()  # Extract posts from the current URL
                
            except Exception as e:
                self.log_debug(f"Error processing URL {url}: {e}")
    
    def extract_posts(self):
        """Extract posts from the current search results, ensuring the correct number of posts are loaded before processing."""
        
        self.log_debug("Extracting posts for the current keyword...")

        post_xpath = "//div[contains(@role,'article')]"
        comment_button_xpath = ".//button[contains(@aria-label,'comments')]"
        load_more_xpath = ".//button[contains(@class,'load-more')]"  

        min_required_posts = 0
        max_load_attempts = 200  
        load_attempt = 0

        while True:
            try:
                posts = self.driver.find_elements(By.XPATH, post_xpath)
                total_loaded = len(posts)
                self.log_debug(f"Total posts loaded: {total_loaded}")

                if total_loaded >= min_required_posts:
                    self.log_debug(f"Loaded at least {min_required_posts} posts, now processing.")
                    break  

                if load_attempt >= max_load_attempts:
                    self.log_debug("Max attempts reached, proceeding with available posts.")
                    break  

                # Click the comment button of the last loaded post to trigger more loads
                last_post_index = total_loaded - 1
                if last_post_index >= 0:
                    try:
                        last_post = posts[last_post_index]
                        comment_buttons = last_post.find_elements(By.XPATH, comment_button_xpath)
                        if comment_buttons:
                            comment_button = comment_buttons[0]  
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", comment_button)
                            time.sleep(1)
                            self.driver.execute_script("arguments[0].click();", comment_button)
                            self.log_debug(f"Scrolled to and clicked comment button on post {last_post_index + 1}.")
                            time.sleep(2)  
                    except Exception:
                        self.log_debug(f"Skipping post {last_post_index + 1} due to missing comment button.")

                # # Click the "Load More" button if available
                # try:
                #     load_more_buttons = self.driver.find_elements(By.XPATH, load_more_xpath)
                #     if load_more_buttons:
                #         load_more_button = load_more_buttons[0]
                #         self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_button)
                #         time.sleep(1)
                #         self.driver.execute_script("arguments[0].click();", load_more_button)
                #         self.log_debug("Scrolled to and clicked 'Load More' button to load more posts.")
                #         time.sleep(3)  
                #     else:
                #         self.log_debug("No 'Load More' button found, assuming no more posts to load.")
                #         break  
                # except Exception:
                #     self.log_debug("Skipping 'Load More' button due to an error.")

                load_attempt += 1
            except Exception as e:
                self.log_debug(f"Unexpected error during loading posts: {e}")
                continue  

        # ✅ Start scraping from the last processed post
        self.log_debug(f"Finished loading {total_loaded} posts. Now starting post processing.")

        for index, post in enumerate(posts, start=1):
            if index < min_required_posts:
                self.log_debug(f"Skipping post {index} as it is already processed.")
                continue  

            try:
                self.log_debug(f"Processing post {index}...")

                try:
                    comment_buttons = post.find_elements(By.XPATH, comment_button_xpath)
                    if comment_buttons:
                        comment_button = comment_buttons[0]
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", comment_button)
                        time.sleep(1)
                        self.driver.execute_script("arguments[0].click();", comment_button)
                        time.sleep(2)
                    else:
                        self.log_debug(f"No comment button found for post {index}, skipping.")
                        continue
                except Exception:
                    self.log_debug(f"Skipping post {index} due to comment button error.")
                    continue

                # Scrape comments for the post
                self.scrape_comments_from_post(post)

                # Save the processed post index
                self.processed_posts.add(index)
                self.save_processed_posts(index)  

                self.log_debug(f"Processed and saved post index: {index}")

            except Exception as e:
                self.log_debug(f"Skipping post {index} due to an error: {e}")
                continue  


    def scrape_comments_from_post(self, post):
        """Scrape all comments from the current post."""
        self.log_debug("Scraping comments from the current post...")
        # Scoped XPaths for comments within the current post
        comment_items_xpath = ".//article[contains(@class,'comments-comment-entity')]"
        name_xpath = ".//h3[contains(@class,'comments-comment-meta__description')]/span[contains(@class,'comments-comment-meta__description-title')]"
        title_xpath = ".//div[contains(@class,'comments-comment-meta__description-subtitle')]"
        linkedin_url_xpath = ".//div[contains(@class,'comments-comment-meta__actor')]/a"
        comment_content_xpath = ".//span[contains(@class,'comment-item')]"
        load_more_xpath = ".//button[contains(@class,'load-more')]"

        processed_comments = set()  # Reset for each post

        while True:
            try:
                # Check if "Load More" button is available
                load_more_button = post.find_elements(By.XPATH, load_more_xpath)
                while True:
                    load_more_button = post.find_elements(By.XPATH, load_more_xpath)
                    if not load_more_button:
                        break  # Exit when no more button found

                    self.log_debug("Clicking 'Load More' button within the post...")
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_button[0])
                    load_more_button[0].click()
                    time.sleep(2)

                    # Scroll to bottom to trigger lazy loading
                    self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(2)

                # Fetch all visible comment items within the post
                comment_items = post.find_elements(By.XPATH, comment_items_xpath)
                self.log_debug(f"Found {len(comment_items)} comment items within the post.")

                for comment in comment_items:
                    try:
                        # Extract a unique identifier for the comment
                        comment_id = comment.get_attribute("data-id") or comment.text.strip()
                        if comment_id in processed_comments:
                            continue

                        # Extract comment details
                        name = comment.find_element(By.XPATH, name_xpath).text.strip()
                        title = comment.find_element(By.XPATH, title_xpath).text.strip()
                        linkedin_urls = comment.find_elements(By.XPATH, linkedin_url_xpath)
                        linkedin_url = linkedin_urls[0].get_attribute("href").strip() if linkedin_urls else ""
                        comment_content = comment.find_element(By.XPATH, comment_content_xpath).text.strip()

                        # Clean extracted data
                        name = name.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')
                        title = title.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')
                        comment_content = comment_content.encode('utf-8', errors='ignore').decode('utf-8', errors='ignore')

                        email_match = re.search(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", comment_content)
                        email = email_match.group() if email_match else None

                        # Check for duplicates in the database before verifying the email
                        if email and self.is_valid_email(email):
                            try:
                                self.ensure_connection()
                                self.cursor = self.conn.cursor()  # recreate cursor if closed
                                self.cursor.execute('SELECT email FROM "mailingList" WHERE email = %s', (email,))
                                existing_email = self.cursor.fetchone()
                            except Exception as e:
                                self.log_debug(f"Error checking email in DB: {e}")
                                existing_email = None


                            if existing_email:
                                self.log_debug(f"Email {email} already exists in the database.")
                            else:
                                # Validate email quality if not in the database
                                if self.validate_email_quality(email, "uRRSTeKLdlQAiQGBCfp0Cu5u1"):
                                    self.save_to_csv(name, title, linkedin_url, email)
                                    self.insert_into_database(name, title, linkedin_url, email)
                                    self.log_debug(f"Extracted: {name}, {title}, {linkedin_url}, {email}")

                        processed_comments.add(comment_id)

                    except Exception as e:
                        self.log_debug(f"Error processing comment: {e}")

                # If "Load More" button is no longer present, exit the loop
                if not load_more_button:
                    break

            except Exception as e:
                self.log_debug(f"Error during extraction: {e}")

        self.log_debug("Finished scraping all comments from the post.")

    def save_to_csv(self, name, title, linkedin_url, email):
        """Insert the valid email into a CSV file."""
        self.log_debug(f"Saving {email} to CSV...")
        # Check if the file exists, create it if not
        if not os.path.exists(self.csv_file):
            df = pd.DataFrame(columns=["Name", "Title", "LinkedIn URL", "Email"])
            df.to_csv(self.csv_file, index=False)

        # Read the existing CSV file
        existing_data = pd.read_csv(self.csv_file)
        # Check for duplicates
        if email not in existing_data["Email"].values:
            new_data = pd.DataFrame([[name, title, linkedin_url, email]], columns=["Name", "Title", "LinkedIn URL", "Email"])
            updated_data = pd.concat([existing_data, new_data], ignore_index=True)
            updated_data.to_csv(self.csv_file, index=False)
            self.log_debug(f"Saved {email} to CSV.")

    def close_connection(self):
        """Close the database connection."""
        self.log_debug("Closing database connection...")
        self.cursor.close()
        self.conn.close()

if __name__ == "__main__":
    scraper = LinkedInPostURLScraper()
    scraper.scrape_post_comments_with_keywords()
    scraper.close_connection()
