import os
import pickle
import requests
import time
import re
import psycopg2
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd

# ===============================
# FILE AND DATABASE CONFIGURATION
# ===============================

# Directory where processed post IDs are stored
PROCESSED_URNS_DIR = "processed_urns2"
# File to track last processed post index
PROCESSED_INDEX_FILE = "processed_posts.txt"
# Output file for storing scraped LinkedIn comments
OUTPUT_FILE = "linkedin_comments.txt"
# Debug log file to track errors and execution details
DEBUG_FILE = "debug_log.txt"
# File containing LinkedIn post URLs for scraping
URLS_FILE = "urls.txt"
# Database connection string (PostgreSQL)
DB_CONNECTION_STRING = "postgresql://beyz-dev:<EMAIL>/beyz-production?sslmode=require"
# Email validation API
EMAIL_VERIFIER_API = "https://api.millionverifier.com/api/v3/?api=uRRSTeKLdlQAiQGBCfp0Cu5u1&email={email}&timeout=10"
# File to store extracted LinkedIn profile URLs
PROFILE_URLS_FILE = "profile_urls.txt"

PROCESSED_PROFILES_FILE = "processed_profiles.txt"

# XPaths for locating elements in LinkedIn posts
profile_url_xpath = ".//div[contains(@class,'update-components-actor')]/a[contains(@class,'update-components-actor__image')]"
post_xpath = "//div[@data-urn]"
comment_button_xpath = ".//button[contains(@aria-label,'comments')]"

# ========================
# LOGGING AND FILE HANDLING
# ========================

def log_debug(message):
    """Logs debug messages to a file."""
    with open(DEBUG_FILE, "a", encoding="utf-8") as f:
        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")

def reset_debug_log():
    """Resets the debug log at the start of each run."""
    open(DEBUG_FILE, "w").close()

def load_urls():
    """Loads LinkedIn URLs from the 'urls.txt' file."""
    if not os.path.exists(URLS_FILE):
        log_debug(f"[ERROR] {URLS_FILE} not found. Exiting.")
        return []
    
    with open(URLS_FILE, "r", encoding="utf-8") as f:
        urls = [line.strip() for line in f.readlines() if line.strip()]
    
    log_debug(f"Loaded {len(urls)} URLs from file.")
    return urls

def load_profile_urls():
    """Loads LinkedIn profile URLs and converts them to recent activity URLs."""
    if not os.path.exists(PROFILE_URLS_FILE):
        return []
    
    with open(PROFILE_URLS_FILE, "r", encoding="utf-8") as f:        
        return [line.strip().split("?")[0] + "/recent-activity/all/" for line in f.readlines() if line.strip()]

def load_urls_from_csv(file_path="posts3.csv"):
    """Loads LinkedIn post URLs from the 'Post' column of a CSV file."""
    log_debug(f"[INFO] Attempting to load post URLs from CSV: {file_path}")
    
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            df = pd.read_csv(f, on_bad_lines='skip')  # skip bad rows

        if "Post" not in df.columns:
            log_debug(f"[ERROR] Column 'Post' not found in CSV headers: {df.columns.tolist()}")
            return []

        urls = df["Post"].dropna().unique().tolist()
        log_debug(f"[SUCCESS] Loaded {len(urls)} unique post URLs from CSV.")
        return urls

    except Exception as e:
        log_debug(f"[ERROR] Failed to load CSV file '{file_path}': {e}")
        return []

# =======================
# LINKEDIN SCRAPING SETUP
# =======================

def get_profile_id(url):
    """Extracts a unique identifier from the LinkedIn URL."""
    log_debug(f"Extracting profile ID from URL: {url}")
    
    if "/in/" in url:
        profile_id = url.split("linkedin.com/in/")[-1].split("/")[0]  # Extract profile ID
        log_debug(f"Extracted profile ID: {profile_id}")
        return profile_id
    elif "/search/results/content/" in url:
        log_debug("Detected search results URL, assigning generic profile ID: search_results")
        return "search_results"  # Use a general identifier for search-based posts
    
    log_debug("URL does not match expected patterns, returning None")
    return None

def save_unique_profile_url(profile_url):
    """Saves a profile URL to a file, ensuring uniqueness."""
    if not os.path.exists(PROFILE_URLS_FILE):
        open(PROFILE_URLS_FILE, "w").close()  # Create file if it doesn’t exist

    with open(PROFILE_URLS_FILE, "r", encoding="utf-8") as f:
        saved_urls = set(line.strip() for line in f.readlines())

    if profile_url and profile_url not in saved_urls:
        with open(PROFILE_URLS_FILE, "a", encoding="utf-8") as f:
            f.write(profile_url + "\n")
        log_debug(f"Saved new profile URL: {profile_url}")
    else:
        log_debug(f"Profile URL already exists: {profile_url}")

def initialize_driver():
    """Initialize WebDriver in headless mode."""
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--no-sandbox")
    # comment out this code if you want to see the chrome ui ( it will take more memory to run if you comment this out )
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    # change this to no .exe if you use mac or linux add back the .exe if you use windows
    chrome_driver_path = os.path.join(os.getcwd(), "chromedriver.exe")
    
    driver = webdriver.Chrome(service=Service(chrome_driver_path), options=chrome_options)
    return driver

def login_to_linkedin(driver, cookies_file="shann5.pkl"):
    """Logs into LinkedIn using stored cookies."""
    driver.get("https://www.linkedin.com/login")
    
    if os.path.exists(cookies_file):
        with open(cookies_file, "rb") as f:
            cookies = pickle.load(f)
        for cookie in cookies:
            driver.add_cookie(cookie)
        driver.refresh()
        time.sleep(3)
        if "feed" in driver.current_url:
            log_debug("Logged in using cookies.")
            return True
    
    log_debug("Manual login required. Perform login and save cookies.")
    WebDriverWait(driver, 60).until(EC.url_contains("feed"))
    with open(cookies_file, "wb") as f:
        pickle.dump(driver.get_cookies(), f)
    return True

# =======================
# DATABASE HANDLING
# =======================

def connect_to_database():
    """Establishes a database connection."""
    try:
        conn = psycopg2.connect(DB_CONNECTION_STRING)
        conn.autocommit = True
        return conn
    except Exception as e:
        log_debug(f"Database connection error: {e}")
        return None

def reconnect_database():
    """Reconnects to the database if the connection is lost."""
    global conn  # Ensure we modify the global connection object

    try:
        if conn:
            conn.close()  # Close existing connection
            log_debug("[INFO] Closed existing database connection.")

        log_debug("[INFO] Attempting to reconnect to the database...")
        conn = psycopg2.connect(DB_CONNECTION_STRING)  # Reinitialize connection
        conn.autocommit = True  # Ensure autocommit is enabled
        cursor = conn.cursor()  # Create a new cursor
        log_debug("[SUCCESS] Database reconnected.")
        return cursor  # Return the new cursor

    except Exception as e:
        log_debug(f"[ERROR] Failed to reconnect to database: {e}")
        return None  # Return None if reconnection fails

def validate_email(email):
    """Validates the email using Million Verifier API."""
    try:
        response = requests.get(EMAIL_VERIFIER_API.format(email=email), timeout=10)
        if response.status_code == 200:
            data = response.json()
            return data.get("result") == "ok"
        log_debug(f"Email validation failed: {response.text}")
        return False
    except Exception as e:
        log_debug(f"Error validating email {email}: {e}")
        return False

def insert_into_database(cursor, name, title, linkedin_url, email):
    """Inserts valid data into the database, with retry logic if connection fails."""
    query = 'INSERT INTO "mailingList" (email, name, title, "linkedinURL") VALUES (%s, %s, %s, %s)'
    retries = 10  # Retry database insert if connection fails

    for attempt in range(retries):
        try:
            cursor.execute(query, (email, name, title, linkedin_url))
            log_debug(f"[SUCCESS] Inserted into database: {email}")
            return True  # Successfully inserted
        except psycopg2.OperationalError:
            log_debug(f"[ERROR] Database connection lost. Attempt {attempt + 1}/{retries}. Reconnecting...")
            cursor = reconnect_database()  # Get a new cursor
            if cursor is None:
                return False  # If reconnection fails, return False
            time.sleep(3)  # Small delay before retrying
        except Exception as e:
            log_debug(f"[ERROR] Failed to insert {email} into database: {e}")
            return False  # Return False if an unexpected error occurs

    log_debug(f"[ERROR] Max retries reached. Failed to insert {email} into database.")
    return False  # If all retries fail, return failure

# ==========================
# COMMENT SCRAPING AND EMAIL EXTRACTION
# ==========================

def get_processed_urns(profile_id):
    """Loads processed URNs from a file for a specific profile or search query."""
    log_debug(f"Loading processed URNs for profile ID: {profile_id}")

    if not os.path.exists(PROCESSED_URNS_DIR):
        log_debug(f"Processed URNs directory does not exist. Creating: {PROCESSED_URNS_DIR}")
        os.makedirs(PROCESSED_URNS_DIR)

    urn_file = os.path.join(PROCESSED_URNS_DIR, f"{profile_id}.txt")
    
    if os.path.exists(urn_file):
        with open(urn_file, "r") as f:
            processed_urns = set(f.read().strip().split("\n"))
            log_debug(f"Loaded {len(processed_urns)} processed URNs for profile ID: {profile_id}")
            return processed_urns
    
    log_debug(f"No processed URNs file found for profile ID: {profile_id}, returning empty set")
    return set()

def save_processed_urn(profile_id, urn):
    """Saves a processed URN to the tracking file."""
    urn_file = os.path.join(PROCESSED_URNS_DIR, f"{profile_id}.txt")
    with open(urn_file, "a") as f:
        f.write(urn + "\n")

def check_email_exists(cursor, email):
    """Checks if an email already exists in the database and retries on connection failure."""
    query = 'SELECT 1 FROM "mailingList" WHERE email = %s'
    retries = 10  # Number of times to retry database connection

    for attempt in range(retries):
        try:
            cursor.execute(query, (email,))
            return cursor.fetchone() is not None  # Return True if email exists, False otherwise
        except psycopg2.OperationalError:
            log_debug(f"[ERROR] Database connection lost. Attempt {attempt + 1}/{retries}. Reconnecting...")
            cursor = reconnect_database()  # Get a new cursor
            if cursor is None:
                return False  # If reconnection fails, return False
            time.sleep(3)  # Small delay before retrying
        except Exception as e:
            log_debug(f"[ERROR] Unexpected error while checking email: {e}")
            return False  # Return False if an unexpected error occurs

    log_debug("[ERROR] Max retries reached. Unable to check email existence.")
    return False  # If all retries fail, assume email does not exist

def load_processed_index():
    """Loads last processed post index from a file."""
    if os.path.exists(PROCESSED_INDEX_FILE):
        with open(PROCESSED_INDEX_FILE, "r") as f:
            index = f.read().strip()
            return int(index) if index.isdigit() else 0
    return 0

def save_processed_index(index):
    """Saves the last processed post index to a file."""
    with open(PROCESSED_INDEX_FILE, "w") as f:
        f.write(str(index))

def get_comment_count(post):
    try:
        comment_button = post.find_element(By.XPATH, comment_button_xpath)
        comment_text = comment_button.text
        count = int(re.search(r'\d+', comment_text.replace(',', '')).group())
        return count
    except Exception:
        return 0

def slow_scroll(driver, batch_size, scroll_pause_time=1, scroll_step=500):
    """Scrolls slowly and stops when the required number of posts are loaded."""
    last_post_count = 0
    scroll_attempts = 0
    max_scroll_attempts =  100  # Prevent infinite scrolling in case of issues

    while scroll_attempts < max_scroll_attempts:
        # Scroll down by a small step
        driver.execute_script(f"window.scrollBy(0, {scroll_step});")
        time.sleep(scroll_pause_time)  # Wait for content to load

        # Check how many posts are loaded
        posts = driver.find_elements(By.XPATH, post_xpath)
        current_post_count = len(posts)
        log_debug(f"Scroll attempt {scroll_attempts + 1}: Loaded {current_post_count} posts so far.")

        # Stop if enough posts are loaded
        if current_post_count >= batch_size:
            log_debug("Required posts loaded. Stopping scroll.")
            break

        # Stop if no new posts are loading
        if current_post_count == last_post_count:
            scroll_attempts += 1
        else:
            scroll_attempts = 0  # Reset attempts if new posts loaded

        last_post_count = current_post_count

    if scroll_attempts >= max_scroll_attempts:
        log_debug("Max scroll attempts reached. Stopping scroll.")


def get_post_urns(driver, search_url, profile_id, batch_size=50):
    """Collects up to 'batch_size' post URNs, skipping already processed ones."""
    log_debug(f"Navigating to {search_url} to collect post URNs...")
    driver.get(search_url)
    time.sleep(5)

    processed_urns = get_processed_urns(profile_id)
    post_urns = set()
    last_post_count = 0  # Track how many posts were loaded last time

    log_debug(f"Starting collection of up to {batch_size} posts from {search_url}. Already processed: {len(processed_urns)}")

    while len(post_urns) < batch_size:
        posts = driver.find_elements(By.XPATH, post_xpath)

        if not posts:
            log_debug("No posts found on the page. Stopping collection.")
            break

        last_post = posts[-1]  # Get the last post element
        current_post_count = len(posts)  # Count currently loaded posts

        log_debug(f"Found {current_post_count} posts, processing them...")

        for post in posts:
            urn = post.get_attribute("data-urn")
            if urn:
                urn_number_match = re.search(r"\d+", urn)
                if urn_number_match:
                    urn_number = urn_number_match.group()
                    if urn_number not in processed_urns:
                        comment_count = get_comment_count(post)
                        if comment_count > 20:
                            post_urns.add(urn_number)
                            log_debug(f"Collected new URN: {urn_number}")
                            try:
                                profile_element = post.find_element(By.XPATH, profile_url_xpath)
                                profile_url = profile_element.get_attribute("href")
                                save_unique_profile_url(profile_url)
                            except Exception as e:
                                log_debug(f"Could not extract profile URL for post {urn_number}: {e}")

                    else:
                        log_debug(f"Skipping already processed URN: {urn_number}")               

        if len(post_urns) >= batch_size:
            log_debug(f"Collected {batch_size} posts. Stopping collection.")
            break

        # If no new posts are loading, stop scrolling
        if current_post_count == last_post_count:
            log_debug("No new posts are loading. Stopping scroll.")
            break

        last_post_count = current_post_count  # Update post count for next check

        # Scroll to the bottom of the page
        try:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            log_debug("Scrolled to the bottom of the page.")
            time.sleep(2)  # Give some time for new posts to load
        except Exception as e:
            log_debug(f"Error scrolling to bottom of page: {e}")


    if not post_urns:
        log_debug("No new posts found for processing.")
    
    log_debug(f"Returning {len(post_urns)} new URNs for processing.")
    return list(post_urns)[:batch_size]  # Ensure batch size limit



def fetch_all_comments(urn, api_key, cursor):
    """Fetches all comments for a LinkedIn post using the API and processes emails."""
    url = "https://linkedin-api8.p.rapidapi.com/get-profile-posts-comments"
    page = 1
    retries = 3  # Maximum retries for failed API calls

    log_debug(f"Fetching comments for URN {urn}")

    while True:
        for attempt in range(retries):
            try:
                querystring = {"urn": urn, "sort": "mostRecent", "page": str(page)}
                headers = {
                    "x-rapidapi-key": api_key,
                    "x-rapidapi-host": "linkedin-api8.p.rapidapi.com"
                }

                log_debug(f"Making API request for page {page} (Attempt {attempt + 1})")
                response = requests.get(url, headers=headers, params=querystring)
                time.sleep(3)  # Delay after each API call to prevent hitting rate limits

                if response.status_code != 200:
                    log_debug(f"[ERROR] API request failed (status {response.status_code}): {response.text}")
                    if attempt < retries - 1:
                        log_debug("Retrying API call...")
                        time.sleep(5)  # Additional delay before retrying
                    else:
                        log_debug("Max retries reached. Skipping post.")
                        return
                else:
                    break  # Successful request, exit retry loop
            except requests.exceptions.RequestException as e:
                log_debug(f"[ERROR] Request exception: {e}")
                time.sleep(5)  # Additional delay before retrying
        else:
            log_debug(f"[ERROR] Skipping post {urn} after {retries} failed attempts.")
            return

        data = response.json()
        comments = data.get("data", [])

        if not comments:
            log_debug(f"[INFO] No comments found on page {page}. Stopping further API calls.")
            break

        found_valid_email = False

        for comment in comments:
            text = comment.get("text", "")
            email_match = re.findall(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", text)
            name = comment['author'].get('name', 'Unknown')
            title = comment['author'].get('title', 'Unknown')
            linkedin_url = comment['author'].get('linkedinUrl', 'Unknown')

            log_debug(f"Processing comment: {text}")

            if email_match:
                email = email_match[0]
                log_debug(f"Extracted email: {email} from comment: {text}")
                if not check_email_exists(cursor, email):
                    log_debug(f"Email {email} not found in database. Validating...")
                    if validate_email(email):
                        log_debug(f"Email {email} passed validation. Checking country for: {linkedin_url}")
                        # country = get_country_from_linkedin_url(linkedin_url)
                        # log_debug(f"Country for {linkedin_url}: {country}")                        
                        insert_into_database(cursor, name, title, linkedin_url, email)
                    else:
                        log_debug(f"Email {email} failed validation.")
                else:
                    log_debug(f"Email {email} already exists in database. Skipping insertion.")
            else:
                log_debug("No valid email found in comment.")

        log_debug(f"[INFO] Processed {len(comments)} comments from page {page}")
        page += 1
        time.sleep(3)  # Delay after each API call to prevent hitting rate limits

def get_country_from_linkedin_url(linkedin_url):
    """Fetches country from a LinkedIn profile using the RapidAPI."""
    API_URL = "https://linkedin-data-api.p.rapidapi.com/get-profile-data-by-url"
    API_KEY = "d4f97cc811msh247a487d06a4331p1abd28jsnd41d7e6ac579"  # Replace with env/config ideally

    headers = {
        "x-rapidapi-key": API_KEY,
        "x-rapidapi-host": "linkedin-api8.p.rapidapi.com"
    }

    try:
        response = requests.get(API_URL, headers=headers, params={"url": linkedin_url}, timeout=10)
        data = response.json()
        if "geo" in data and "country" in data["geo"]:
            return data["geo"]["country"]
    except Exception as e:
        log_debug(f"[ERROR] Failed to fetch country for {linkedin_url}: {e}")
    
    return "Unknown"


def main():
    reset_debug_log()
    
    print("Choose input source:")
    print("1 - Use LinkedIn post URLs (urls.txt)")
    print("2 - Use LinkedIn profile URLs (profile_urls.txt)")
    print("3 - Use LinkedIn post URLs from posts3.csv")

    choice = input("Enter your choice (1/2/3): ").strip()

    log_debug("Starting LinkedIn scraper.")
    
    urls = load_urls()
    all_profiles = load_profile_urls()
    if not urls:
        log_debug("No URLs found in the input file. Exiting.")
        return
    
    driver = initialize_driver()
    login_to_linkedin(driver)

    
    conn = connect_to_database()
    if conn is None:
        log_debug("Database connection failed. Exiting.")
        return
    cursor = conn.cursor()    

    
    api_key = "d4f97cc811msh247a487d06a4331p1abd28jsnd41d7e6ac579"
    if choice == "1":
        for url in urls:
            profile_id = get_profile_id(url)
            log_debug(f"Processing LinkedIn page: {url} (Profile ID: {profile_id})")

            post_urns = get_post_urns(driver, url, profile_id)

            for urn in post_urns:
                fetch_all_comments(urn, api_key, cursor)
                save_processed_urn(profile_id, urn)
    
    elif choice == "2":
        for url in all_profiles:            
            profile_id = get_profile_id(url)
            post_urns = get_post_urns(driver, url, profile_id)

            for urn in post_urns:
                fetch_all_comments(urn, api_key, cursor)
                save_processed_urn(profile_id, urn)

    elif choice == "3":
        urls = load_urls_from_csv("posts3.csv")  # Update with actual filename
        for url in urls:
            profile_id = get_profile_id(url) or "csv_post"
            post_urns = get_post_urns(driver, url, profile_id)
            for urn in post_urns:
                fetch_all_comments(urn, api_key, cursor)
                save_processed_urn(profile_id, urn)
    
    cursor.close()
    conn.close()    
    log_debug("Scraper finished.")

if __name__ == "__main__":
    main()
