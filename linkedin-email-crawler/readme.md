# LinkedIn Scraper API Documentation

## Overview

This LinkedIn scraper is a Python-based tool that automates the extraction of comments and email addresses from LinkedIn posts. It uses Selenium WebDriver for web automation, RapidAPI for LinkedIn data access, and PostgreSQL for data storage.

## Features

- **Multi-source input**: Supports URLs from text files, CSV files, and profile URLs
- **Email extraction**: Automatically extracts and validates email addresses from comments
- **Database integration**: Stores validated data in PostgreSQL database
- **Progress tracking**: Maintains processed URNs to avoid duplicate processing
- **Email validation**: Uses Million Verifier API for email validation
- **Automated login**: Uses stored cookies for LinkedIn authentication
- **Error handling**: Comprehensive logging and retry mechanisms

## Prerequisites

### Software Requirements

- Python 3.7+
- Google Chrome browser
- ChromeDriver (placed in project directory)
- PostgreSQL database

### Python Dependencies

```bash
pip install selenium webdriver-manager pandas psycopg2-binary requests
```

### API Keys Required

- **RapidAPI LinkedIn API**: For fetching post comments
- **Million Verifier API**: For email validation

## Configuration

### File Structure

```
project/
├── linkedin_scraper_api.py
├── chromedriver.exe          # Chrome driver (Windows)
├── shann5.pkl               # LinkedIn cookies file
├── urls.txt                 # LinkedIn post URLs
├── posts3.csv              # CSV with post URLs
├── profile_urls.txt        # LinkedIn profile URLs
├── processed_urns2/        # Directory for tracking processed posts
├── processed_posts.txt     # Last processed post index
├── processed_profiles.txt  # Processed profiles tracking
├── linkedin_comments.txt   # Output file
└── debug_log.txt          # Debug logging
```

### Environment Configuration

Update these constants in the script:

```python
# Database connection
DB_CONNECTION_STRING = "****************************************"

# API endpoints
EMAIL_VERIFIER_API = "https://api.millionverifier.com/api/v3/?api=YOUR_API_KEY&email={email}&timeout=10"

# Chrome driver path (remove .exe for Mac/Linux)
chrome_driver_path = os.path.join(os.getcwd(), "chromedriver.exe")
```

## Input Sources

### 1. URLs from Text File (urls.txt)

```
https://www.linkedin.com/posts/username_activity-*********
https://www.linkedin.com/posts/company_activity-*********
```

### 2. Profile URLs (profile_urls.txt)

```
https://www.linkedin.com/in/john-doe/
https://www.linkedin.com/in/jane-smith/
```

### 3. CSV File (posts3.csv)

Must contain a "Post" column with LinkedIn post URLs:

```csv
Post,Other_Column
https://www.linkedin.com/posts/username_activity-*********,data
https://www.linkedin.com/posts/company_activity-*********,data
```

## Database Schema

The script expects a PostgreSQL table with the following structure:

```sql
CREATE TABLE "mailingList" (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    title VARCHAR(255),
    "linkedinURL" VARCHAR(500)
);
```

## Usage

### Initial Setup

1. **Install ChromeDriver**:

   - Download ChromeDriver matching your Chrome version
   - Place in project directory as `chromedriver.exe` (Windows) or `chromedriver` (Mac/Linux)

2. **LinkedIn Authentication**:

   - Run the script for the first time
   - Manually log into LinkedIn when prompted
   - Cookies will be saved automatically for future runs

3. **Prepare Input Files**:
   - Create `urls.txt`, `profile_urls.txt`, or `posts3.csv` as needed
   - Ensure URLs are valid LinkedIn post or profile URLs

### Running the Scraper

```bash
python linkedin_scraper_api.py
```

The script will prompt you to choose an input source:

```
Choose input source:
1 - Use LinkedIn post URLs (urls.txt)
2 - Use LinkedIn profile URLs (profile_urls.txt)
3 - Use LinkedIn post URLs from posts3.csv
Enter your choice (1/2/3):
```

## Core Functions

### Authentication

- `login_to_linkedin(driver, cookies_file)`: Handles LinkedIn login using stored cookies
- `initialize_driver()`: Sets up Chrome WebDriver with optimal settings

### Data Collection

- `get_post_urns(driver, search_url, profile_id, batch_size)`: Collects post URNs from LinkedIn pages
- `fetch_all_comments(urn, api_key, cursor)`: Retrieves all comments for a specific post
- `get_comment_count(post)`: Counts comments on a post to filter high-engagement content

### Data Processing

- `validate_email(email)`: Validates emails using Million Verifier API
- `insert_into_database(cursor, name, title, linkedin_url, email)`: Stores validated data in database
- `check_email_exists(cursor, email)`: Prevents duplicate email entries

### Progress Tracking

- `get_processed_urns(profile_id)`: Loads already processed post URNs
- `save_processed_urn(profile_id, urn)`: Tracks completed posts
- `load_processed_index()`: Maintains processing position

## Configuration Options

### Chrome Driver Settings

```python
chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--headless")          # Comment out to see browser
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
```

### Batch Processing

- Default batch size: 50 posts per profile
- Minimum comment threshold: 20 comments per post
- API delay: 3 seconds between requests

### Scroll Behavior

- Scroll step: 500 pixels
- Pause time: 1 second
- Max scroll attempts: 100

## Error Handling

### Database Reconnection

The script includes automatic database reconnection with retry logic:

- Maximum retries: 10 attempts
- 3-second delay between retries
- Automatic cursor recreation

### API Rate Limiting

- 3-second delays between API calls
- 3 retry attempts for failed requests
- 5-second additional delay on failures

### Logging

All operations are logged to `debug_log.txt` with timestamps:

```
2024-01-15 10:30:45 - Loaded 25 URLs from file.
2024-01-15 10:30:50 - Logged in using cookies.
2024-01-15 10:31:00 - Collected new URN: 7150234567890123456
```

## Performance Considerations

### Memory Usage

- Headless mode reduces memory consumption
- Processed URNs are tracked to avoid reprocessing
- Database connections are properly closed

### Rate Limiting

- Built-in delays prevent API rate limiting
- Scroll behavior is optimized to avoid detection
- Email validation is throttled

### Scalability

- Batch processing prevents memory overflow
- Progress tracking allows resuming interrupted runs
- Separate tracking files per profile/search query

## Troubleshooting

### Common Issues

1. **ChromeDriver Compatibility**

   ```
   Error: ChromeDriver version mismatch
   Solution: Download matching ChromeDriver version
   ```

2. **LinkedIn Login Issues**

   ```
   Error: Cannot access LinkedIn feed
   Solution: Delete cookies file and re-authenticate
   ```

3. **Database Connection Errors**

   ```
   Error: Database connection failed
   Solution: Check connection string and database availability
   ```

4. **API Rate Limiting**
   ```
   Error: Too many requests
   Solution: Increase delays in fetch_all_comments function
   ```

### Debug Logging

Monitor `debug_log.txt` for detailed execution information:

- URL processing status
- Database operations
- API call results
- Error messages with timestamps

## Security Notes

### Sensitive Data

- Store API keys in environment variables
- Use secure database connections (SSL)
- Regularly rotate authentication cookies

### LinkedIn Terms of Service

- Respect LinkedIn's robots.txt
- Implement appropriate delays
- Avoid excessive scraping volumes

### Data Privacy

- Follow GDPR/CCPA guidelines for email collection
- Implement data retention policies
- Secure database access

## Maintenance

### Regular Tasks

1. Update ChromeDriver regularly
2. Monitor API quota usage
3. Clean up log files
4. Backup processed URN files
5. Validate database integrity

### Updates

- Check for Selenium updates
- Monitor LinkedIn layout changes
- Update XPath selectors if needed
- Review API documentation changes

## Output

The scraper generates:

- **Database records**: Validated emails with associated profile data
- **Log files**: Detailed execution logs for monitoring
- **Progress files**: Tracking processed content
- **Profile URLs**: Collected LinkedIn profiles for future processing

## Limitations

- Requires active LinkedIn account with appropriate access
- Subject to LinkedIn's anti-bot measures
- API rate limits may affect processing speed
- Email validation depends on third-party service availability
