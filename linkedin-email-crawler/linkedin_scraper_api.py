import os
import pickle
import requests
import time
import re
import psycopg2
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd
import csv
from dotenv import load_dotenv
load_dotenv()


# ===============================
# FILE AND DATABASE CONFIGURATION
# ===============================

# Directory where processed post IDs are stored
PROCESSED_URNS_DIR = "processed_urns2"
# File to track last processed post index
PROCESSED_INDEX_FILE = "processed_posts.txt"
# Output file for storing scraped LinkedIn comments
OUTPUT_FILE = "linkedin_comments.txt"
# Debug log file to track errors and execution details
DEBUG_FILE = "debug_log.txt"
# File containing LinkedIn post URLs for scraping
URLS_FILE = "urls.txt"
# Database connection string (PostgreSQL)
DB_CONNECTION_STRING = os.getenv("DB_CONNECTION_STRING")
# Email validation API
EMAIL_VERIFIER_API = os.getenv("EMAIL_VERIFIER_API")
# File to store extracted LinkedIn profile URLs
PROFILE_URLS_FILE = "profile_urls.txt"
PROCESSED_CSV_URLS_FILE = "processed_csv_urls.txt"


PROCESSED_PROFILES_FILE = "processed_profiles.txt"

# XPaths for locating elements in LinkedIn posts
profile_url_xpath = ".//div[contains(@class,'update-components-actor')]/a[contains(@class,'update-components-actor__image')]"
post_xpath = "//div[@data-urn]"
comment_button_xpath = ".//button[contains(@aria-label,'comments')]"

# ========================
# LOGGING AND FILE HANDLING
# ========================

def log_debug(message):
    """Logs debug messages to a file."""
    with open(DEBUG_FILE, "a", encoding="utf-8") as f:
        f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")

def reset_debug_log():
    """Resets the debug log at the start of each run."""
    open(DEBUG_FILE, "w").close()

def load_urls():
    """Loads LinkedIn URLs from the 'urls.txt' file."""
    if not os.path.exists(URLS_FILE):
        log_debug(f"[ERROR] {URLS_FILE} not found. Exiting.")
        return []
    
    with open(URLS_FILE, "r", encoding="utf-8") as f:
        urls = [line.strip() for line in f.readlines() if line.strip()]
    
    log_debug(f"Loaded {len(urls)} URLs from file.")
    return urls

def load_profile_urls():
    """Loads LinkedIn profile URLs and converts them to recent activity URLs."""
    if not os.path.exists(PROFILE_URLS_FILE):
        return []
    
    with open(PROFILE_URLS_FILE, "r", encoding="utf-8") as f:        
        return [line.strip().split("?")[0] + "/recent-activity/all/" for line in f.readlines() if line.strip()]

def load_urls_from_csv(file_path="posts3.csv"):
    """Loads LinkedIn post URLs from the 'Post' column of a CSV file."""
    log_debug(f"[INFO] Attempting to load post URLs from CSV: {file_path}")
    
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            df = pd.read_csv(f, on_bad_lines='skip')  # skip bad rows

        if "Post" not in df.columns:
            log_debug(f"[ERROR] Column 'Post' not found in CSV headers: {df.columns.tolist()}")
            return []

        urls = df["Post"].dropna().unique().tolist()
        log_debug(f"[SUCCESS] Loaded {len(urls)} unique post URLs from CSV.")
        return urls

    except Exception as e:
        log_debug(f"[ERROR] Failed to load CSV file '{file_path}': {e}")
        return []

# =======================
# LINKEDIN SCRAPING SETUP
# =======================

def extract_username_from_url(profile_url):
    """Extracts LinkedIn username from a profile URL."""
    if "/in/" in profile_url:
        return profile_url.split("/in/")[1].split("/")[0]
    return None

def load_processed_profiles():
    """Load usernames of profiles already scraped."""
    if not os.path.exists(PROCESSED_PROFILES_FILE):
        return set()
    with open(PROCESSED_PROFILES_FILE, "r", encoding="utf-8") as f:
        return set(line.strip() for line in f.readlines())

def save_processed_profile(username):
    """Mark a profile as processed to avoid re-scraping."""
    with open(PROCESSED_PROFILES_FILE, "a", encoding="utf-8") as f:
        f.write(username + "\n")

def load_kol_profiles_from_csv(file_path="posts3.csv"):
    """Loads KOL profile URLs from the CSV file (expects a 'KOL Profile' column)."""
    try:
        df = pd.read_csv(file_path, encoding="utf-8", on_bad_lines="skip")
    except UnicodeDecodeError:
        log_debug("[WARN] UTF-8 decoding failed. Retrying with 'ISO-8859-1' encoding...")
        try:
            df = pd.read_csv(file_path, encoding="ISO-8859-1", on_bad_lines="skip")
        except Exception as e:
            log_debug(f"[ERROR] Failed to load KOL profiles with fallback: {e}")
            return []

    if "KOL Profile" not in df.columns:
        log_debug("[ERROR] 'KOL Profile' column not found.")
        return []

    urls = df["KOL Profile"].dropna().unique().tolist()
    filtered_urls = [
        url for url in urls
        if "linkedin.com/in/" in url and not any(x in url for x in ["company", "school", "about", "no"])
    ]
    return filtered_urls

def get_urn_from_username(username, api_key):
    """Gets URN from Fresh LinkedIn Scraper API given a username"""
    import http.client
    import json

    conn = http.client.HTTPSConnection("fresh-linkedin-scraper-api.p.rapidapi.com")
    headers = {
        "x-rapidapi-key": api_key,
        "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com"
    }

    endpoint = f"/api/v1/user/contact?username={username}"
    try:
        conn.request("GET", endpoint, headers=headers)
        res = conn.getresponse()
        data = res.read().decode("utf-8")
        response = json.loads(data)

        if response.get("success") and "data" in response and "urn" in response["data"]:
            return response["data"]["urn"]
        else:
            log_debug(f"[ERROR] Failed to fetch URN for {username}: {response.get('message')}")
            return None
    except Exception as e:
        log_debug(f"[ERROR] Exception while fetching URN: {e}")
        return None

def get_post_ids_from_profile(profile_urn, api_key):
    """Fetch post IDs (URNs) from Fresh LinkedIn Scraper API based on profile URN, filtering by ≥100 comments."""
    import http.client
    import json
    import time

    conn = http.client.HTTPSConnection("fresh-linkedin-scraper-api.p.rapidapi.com")
    headers = {
        "x-rapidapi-key": api_key,
        "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com"
    }

    collected_post_ids = []
    page = 1
    max_pages = 10  # Increase if needed

    while page <= max_pages:
        endpoint = f"/api/v1/user/posts?urn={profile_urn}&page={page}"
        try:
            conn.request("GET", endpoint, headers=headers)
            res = conn.getresponse()
            data = res.read().decode("utf-8")

            try:
                json_data = json.loads(data)
            except Exception as e:
                log_debug(f"[ERROR] Failed to parse JSON on page {page}: {e}")
                break

            posts = json_data.get("data", [])
            if not posts:
                log_debug(f"[INFO] No posts found on page {page}.")
                break

            log_debug(f"[INFO] Page {page}: {len(posts)} posts returned.")

            for post in posts:
                post_id = post.get("id")
                comments = post.get("activity", {}).get("num_comments", 0)

                if post_id and comments >= 100:
                    collected_post_ids.append(post_id)
                    log_debug(f"[COLLECTED] Post ID: {post_id} | Comments: {comments}")
                else:
                    log_debug(f"[SKIPPED] Post ID: {post_id} | Comments: {comments}")

            page += 1
            time.sleep(1)

        except Exception as e:
            log_debug(f"[ERROR] API error on page {page}: {e}")
            break

    log_debug(f"[SUMMARY] {len(collected_post_ids)} posts with ≥100 comments found for URN: {profile_urn}")
    return collected_post_ids


def get_profile_id(url):
    """Extracts a unique identifier from the LinkedIn URL."""
    log_debug(f"Extracting profile ID from URL: {url}")
    
    if "/in/" in url:
        profile_id = url.split("linkedin.com/in/")[-1].split("/")[0]  # Extract profile ID
        log_debug(f"Extracted profile ID: {profile_id}")
        return profile_id
    elif "/search/results/content/" in url:
        log_debug("Detected search results URL, assigning generic profile ID: search_results")
        return "search_results"  # Use a general identifier for search-based posts
    
    log_debug("URL does not match expected patterns, returning None")
    return None

def load_processed_csv_urls():
    """Loads already processed post URLs from a file."""
    if not os.path.exists(PROCESSED_CSV_URLS_FILE):
        return set()
    with open(PROCESSED_CSV_URLS_FILE, "r", encoding="utf-8") as f:
        return set(line.strip() for line in f.readlines())

def save_processed_csv_url(url):
    """Appends a post URL to the processed tracker file."""
    with open(PROCESSED_CSV_URLS_FILE, "a", encoding="utf-8") as f:
        f.write(url.strip() + "\n")

def load_post_urls_from_csv(file_path="posts3.csv"):
    """Loads post URLs from a CSV with a 'Post' column."""
    urls = []
    with open(file_path, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            url = row.get("Post")
            if url:
                urls.append(url.strip())
    return urls


def save_unique_profile_url(profile_url):
    """Saves a profile URL to a file, ensuring uniqueness."""
    if not os.path.exists(PROFILE_URLS_FILE):
        open(PROFILE_URLS_FILE, "w").close()  # Create file if it doesn’t exist

    with open(PROFILE_URLS_FILE, "r", encoding="utf-8") as f:
        saved_urls = set(line.strip() for line in f.readlines())

    if profile_url and profile_url not in saved_urls:
        with open(PROFILE_URLS_FILE, "a", encoding="utf-8") as f:
            f.write(profile_url + "\n")
        log_debug(f"Saved new profile URL: {profile_url}")
    else:
        log_debug(f"Profile URL already exists: {profile_url}")

def initialize_driver():
    """Initialize WebDriver for macOS using a specific chromedriver path."""
    chrome_options = webdriver.ChromeOptions()
    chrome_options.page_load_strategy = 'eager'

    # Optional: run headless if needed
    chrome_options.add_argument("--headless=new")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-gpu")

    # Path to chromedriver on macOS (adjust if different)
    driver_path = "/Users/<USER>/dev/server/chromedriver-mac-arm64/chromedriver"

    driver = webdriver.Chrome(service=Service(driver_path), options=chrome_options)
    driver.set_page_load_timeout(60)
    return driver


def login_to_linkedin(driver, cookies_file="shann5.pkl"):
    """Logs into LinkedIn using stored cookies."""
    driver.get("https://www.linkedin.com/login")
    
    if os.path.exists(cookies_file):
        with open(cookies_file, "rb") as f:
            cookies = pickle.load(f)
        for cookie in cookies:
            driver.add_cookie(cookie)
        driver.refresh()
        time.sleep(3)
        if "feed" in driver.current_url:
            log_debug("Logged in using cookies.")
            return True
    
    log_debug("Manual login required. Perform login and save cookies.")
    WebDriverWait(driver, 60).until(EC.url_contains("feed"))
    with open(cookies_file, "wb") as f:
        pickle.dump(driver.get_cookies(), f)
    return True

# =======================
# DATABASE HANDLING
# =======================

def connect_to_database():
    """Establishes a database connection."""
    try:
        conn = psycopg2.connect(DB_CONNECTION_STRING)
        conn.autocommit = True
        return conn
    except Exception as e:
        log_debug(f"Database connection error: {e}")
        return None

def reconnect_database():
    """Reconnects to the database if the connection is lost."""
    global conn  # Ensure we modify the global connection object

    try:
        if conn:
            conn.close()  # Close existing connection
            log_debug("[INFO] Closed existing database connection.")

        log_debug("[INFO] Attempting to reconnect to the database...")
        conn = psycopg2.connect(DB_CONNECTION_STRING)  # Reinitialize connection
        conn.autocommit = True  # Ensure autocommit is enabled
        cursor = conn.cursor()  # Create a new cursor
        log_debug("[SUCCESS] Database reconnected.")
        return cursor  # Return the new cursor

    except Exception as e:
        log_debug(f"[ERROR] Failed to reconnect to database: {e}")
        return None  # Return None if reconnection fails

def validate_email(email):
    """Validates the email using Million Verifier API."""
    try:
        response = requests.get(EMAIL_VERIFIER_API.format(email=email), timeout=10)
        if response.status_code == 200:
            data = response.json()
            return data.get("result") == "ok"
        log_debug(f"Email validation failed: {response.text}")
        return False
    except Exception as e:
        log_debug(f"Error validating email {email}: {e}")
        return False

def insert_into_database(cursor, name, title, linkedin_url, email):
    """Inserts valid data into the database, with retry logic if connection fails."""
    query = '''
        INSERT INTO "linkedinEmails" (email, name, title, "linkedinURL", created_at)
        VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
    '''
    retries = 10  # Retry database insert if connection fails

    for attempt in range(retries):
        try:
            cursor.execute(query, (email, name, title, linkedin_url))
            log_debug(f"[SUCCESS] Inserted into database: {email}")
            return True  # Successfully inserted
        except psycopg2.OperationalError:
            log_debug(f"[ERROR] Database connection lost. Attempt {attempt + 1}/{retries}. Reconnecting...")
            cursor = reconnect_database()  # Get a new cursor
            if cursor is None:
                return False  # If reconnection fails, return False
            time.sleep(3)  # Small delay before retrying
        except Exception as e:
            log_debug(f"[ERROR] Failed to insert {email} into database: {e}")
            return False  # Return False if an unexpected error occurs

    log_debug(f"[ERROR] Max retries reached. Failed to insert {email} into database.")
    return False  # If all retries fail, return failure

# ==========================
# COMMENT SCRAPING AND EMAIL EXTRACTION
# ==========================

def get_processed_urns(profile_id):
    """Loads processed URNs from a file for a specific profile or search query."""
    log_debug(f"Loading processed URNs for profile ID: {profile_id}")

    if not os.path.exists(PROCESSED_URNS_DIR):
        log_debug(f"Processed URNs directory does not exist. Creating: {PROCESSED_URNS_DIR}")
        os.makedirs(PROCESSED_URNS_DIR)

    urn_file = os.path.join(PROCESSED_URNS_DIR, f"{profile_id}.txt")
    
    if os.path.exists(urn_file):
        with open(urn_file, "r") as f:
            processed_urns = set(f.read().strip().split("\n"))
            log_debug(f"Loaded {len(processed_urns)} processed URNs for profile ID: {profile_id}")
            return processed_urns
    
    log_debug(f"No processed URNs file found for profile ID: {profile_id}, returning empty set")
    return set()

def save_processed_urn(profile_id, urn):
    """Saves a processed URN to the tracking file."""
    urn_file = os.path.join(PROCESSED_URNS_DIR, f"{profile_id}.txt")
    with open(urn_file, "a") as f:
        f.write(urn + "\n")

def check_email_exists(email):
    """Re-connects to DB and checks if email exists each time."""
    query = 'SELECT 1 FROM "mailingList" WHERE email = %s'

    try:
        conn = psycopg2.connect(DB_CONNECTION_STRING)
        conn.autocommit = True
        with conn.cursor() as cursor:
            cursor.execute(query, (email,))
            result = cursor.fetchone()
            return result is not None
    except Exception as e:
        log_debug(f"[ERROR] Failed to check email {email}: {e}")
        return False
    finally:
        if conn:
            conn.close()

def load_processed_index():
    """Loads last processed post index from a file."""
    if os.path.exists(PROCESSED_INDEX_FILE):
        with open(PROCESSED_INDEX_FILE, "r") as f:
            index = f.read().strip()
            return int(index) if index.isdigit() else 0
    return 0

def save_processed_index(index):
    """Saves the last processed post index to a file."""
    with open(PROCESSED_INDEX_FILE, "w") as f:
        f.write(str(index))

def get_comment_count(post):
    try:
        comment_button = post.find_element(By.XPATH, comment_button_xpath)
        comment_text = comment_button.text
        count = int(re.search(r'\d+', comment_text.replace(',', '')).group())
        return count
    except Exception:
        return 0

def slow_scroll(driver, batch_size, scroll_pause_time=1, scroll_step=500):
    """Scrolls slowly and stops when the required number of posts are loaded."""
    last_post_count = 0
    scroll_attempts = 0
    max_scroll_attempts =  100  # Prevent infinite scrolling in case of issues

    while scroll_attempts < max_scroll_attempts:
        # Scroll down by a small step
        driver.execute_script(f"window.scrollBy(0, {scroll_step});")
        time.sleep(scroll_pause_time)  # Wait for content to load

        # Check how many posts are loaded
        posts = driver.find_elements(By.XPATH, post_xpath)
        current_post_count = len(posts)
        log_debug(f"Scroll attempt {scroll_attempts + 1}: Loaded {current_post_count} posts so far.")

        # Stop if enough posts are loaded
        if current_post_count >= batch_size:
            log_debug("Required posts loaded. Stopping scroll.")
            break

        # Stop if no new posts are loading
        if current_post_count == last_post_count:
            scroll_attempts += 1
        else:
            scroll_attempts = 0  # Reset attempts if new posts loaded

        last_post_count = current_post_count

    if scroll_attempts >= max_scroll_attempts:
        log_debug("Max scroll attempts reached. Stopping scroll.")

def safe_driver_get(driver, url, retries=3, delay=5):
    for i in range(retries):
        try:
            log_debug(f"[INFO] Attempting to load URL (attempt {i+1}): {url}")
            driver.get(url)
            return True
        except Exception as e:
            log_debug(f"[WARN] driver.get() failed on attempt {i+1}: {e}")
            time.sleep(delay)
    return False


def get_post_urns(driver, search_url, profile_id, batch_size=50):
    """Collects up to 'batch_size' post URNs, skipping already processed ones."""
    log_debug(f"Navigating to {search_url} to collect post URNs...")
    if not safe_driver_get(driver, search_url):
        log_debug(f"[FATAL] Failed to load URL after retries: {search_url}")
        return []

    time.sleep(5)

    processed_urns = get_processed_urns(profile_id)
    post_urns = set()
    last_post_count = 0  # Track how many posts were loaded last time

    log_debug(f"Starting collection of up to {batch_size} posts from {search_url}. Already processed: {len(processed_urns)}")

    while len(post_urns) < batch_size:
        posts = driver.find_elements(By.XPATH, post_xpath)

        if not posts:
            log_debug("No posts found on the page. Stopping collection.")
            break

        last_post = posts[-1]  # Get the last post element
        current_post_count = len(posts)  # Count currently loaded posts

        log_debug(f"Found {current_post_count} posts, processing them...")

        for post in posts:
            urn = post.get_attribute("data-urn")
            if urn:
                urn_number_match = re.search(r"\d+", urn)
                if urn_number_match:
                    urn_number = urn_number_match.group()
                    if urn_number not in processed_urns:
                        comment_count = get_comment_count(post)
                        if comment_count > 20:
                            post_urns.add(urn_number)
                            log_debug(f"Collected new URN: {urn_number}")
                            try:
                                profile_element = post.find_element(By.XPATH, profile_url_xpath)
                                profile_url = profile_element.get_attribute("href")
                                save_unique_profile_url(profile_url)
                            except Exception as e:
                                log_debug(f"Could not extract profile URL for post {urn_number}: {e}")

                    else:
                        log_debug(f"Skipping already processed URN: {urn_number}")               

        if len(post_urns) >= batch_size:
            log_debug(f"Collected {batch_size} posts. Stopping collection.")
            break

        # If no new posts are loading, stop scrolling
        if current_post_count == last_post_count:
            log_debug("No new posts are loading. Stopping scroll.")
            break

        last_post_count = current_post_count  # Update post count for next check

        # Scroll to the bottom of the page
        try:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            log_debug("Scrolled to the bottom of the page.")
            time.sleep(2)  # Give some time for new posts to load
        except Exception as e:
            log_debug(f"Error scrolling to bottom of page: {e}")


    if not post_urns:
        log_debug("No new posts found for processing.")
    
    log_debug(f"Returning {len(post_urns)} new URNs for processing.")
    return list(post_urns)[:batch_size]  # Ensure batch size limit

def fetch_comments_from_fresh_api(urn, cursor, api_key):
    """Fetches all comments from Fresh LinkedIn API using pagination until no more data."""
    import http.client
    import json

    conn = http.client.HTTPSConnection("fresh-linkedin-scraper-api.p.rapidapi.com")
    headers = {
        "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com",
        "x-rapidapi-key": api_key
    }

    page = 1
    while True:
        endpoint = f"/api/v1/user/comments?urn={urn}&page={page}"
        try:
            conn.request("GET", endpoint, headers=headers)
            res = conn.getresponse()
            raw_data = res.read().decode("utf-8")

            try:
                response_json = json.loads(raw_data)
            except Exception as e:
                log_debug(f"[ERROR] JSON parsing failed on page {page}: {e}")
                break

            if not response_json.get("success"):
                log_debug(f"[ERROR] API failed for URN {urn}, page {page}: {response_json.get('message')}")
                break

            comments = response_json.get("data", [])
            if not comments:
                log_debug(f"[INFO] No comments found on page {page} for URN {urn}")
                break  # No more pages

            for comment_obj in comments:
                comment_text = comment_obj.get("comment", "")
                email_matches = re.findall(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", comment_text)

                author = comment_obj.get("author", {})
                name = author.get("full_name", "Unknown")
                linkedin_url = author.get("url", "Unknown")

                log_debug(f"[COMMENT] {comment_text}")

                for email in email_matches:
                    log_debug(f"Found email: {email} from comment")

                    if not check_email_exists(cursor, email):
                        if validate_email(email):
                            log_debug(f"✅ Validated email: {email} — inserting...")
                            insert_into_database(cursor, name, None, linkedin_url, email)
                        else:
                            log_debug(f"❌ Invalid email: {email}")
                    else:
                        log_debug(f"Email already exists: {email}")

            # Move to next page if pagination_token exists
            pagination_token = response_json.get("pagination_token")
            if not pagination_token:
                log_debug("No more pagination tokens. Finished scraping comments.")
                break

            page += 1
            time.sleep(2)

        except Exception as e:
            log_debug(f"[ERROR] Exception during API call on page {page}: {e}")
            break

def fetch_post_comments_from_fresh_api(post_id, cursor, api_key):
    """Fetches all comments from Fresh LinkedIn API by post_id and extracts/validates emails."""
    import http.client
    import json
    import re
    import time

    conn = http.client.HTTPSConnection("fresh-linkedin-scraper-api.p.rapidapi.com")
    headers = {
        "x-rapidapi-host": "fresh-linkedin-scraper-api.p.rapidapi.com",
        "x-rapidapi-key": api_key
    }

    page = 1
    total_valid_emails = 0
    max_pages = 10  # optional: avoid infinite loop

    while True:
        endpoint = f"/api/v1/post/comments?post_id={post_id}&page={page}&sort_order=relevance"
        try:
            conn.request("GET", endpoint, headers=headers)
            res = conn.getresponse()
            raw_data = res.read().decode("utf-8")

            try:
                response_json = json.loads(raw_data)
            except Exception as e:
                log_debug(f"[ERROR] JSON parsing failed on page {page}: {e}")
                break

            if not response_json.get("success"):
                log_debug(f"[ERROR] API failed for post_id {post_id}, page {page}: {response_json.get('message')}")
                break

            comments = response_json.get("data", [])
            if not comments:
                log_debug(f"[INFO] No comments found on page {page} for post_id {post_id}")
                break

            found_valid_in_page = False  # Track for page 1
            for comment_obj in comments:
                comment_text = comment_obj.get("comment", "")
                email_matches = re.findall(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", comment_text)

                commenter = comment_obj.get("commenter", {})
                name = commenter.get("name", "Unknown")
                linkedin_url = commenter.get("url", "Unknown")

                log_debug(f"[COMMENT] {comment_text}")

                for email in email_matches:
                    if not check_email_exists(cursor, email):
                        if validate_email(email):
                            insert_into_database(cursor, name, None, linkedin_url, email)
                            found_valid_in_page = True
                            total_valid_emails += 1
                        else:
                            log_debug(f"❌ Invalid email: {email}")
                    else:
                        log_debug(f"Email already exists: {email}")

            # If we're still on page 1 and no email was found → skip further pages
            if page == 1 and not found_valid_in_page:
                log_debug(f"[SKIP] No valid email on page 1 of post {post_id}. Skipping further scraping.")
                break

            if not response_json.get("has_more") or page >= max_pages:
                break

            page += 1
            time.sleep(2)

        except Exception as e:
            log_debug(f"[ERROR] Exception during API call on page {page}: {e}")
            break

    return total_valid_emails


def fetch_all_comments(urn, api_key, cursor):
    """Fetches all comments for a LinkedIn post using the API and processes emails."""
    url = "https://linkedin-api8.p.rapidapi.com/get-profile-posts-comments"
    page = 1
    retries = 3  # Maximum retries for failed API calls

    log_debug(f"Fetching comments for URN {urn}")

    while True:
        for attempt in range(retries):
            try:
                querystring = {"urn": urn, "sort": "mostRecent", "page": str(page)}
                headers = {
                    "x-rapidapi-key": api_key,
                    "x-rapidapi-host": "linkedin-api8.p.rapidapi.com"
                }

                log_debug(f"Making API request for page {page} (Attempt {attempt + 1})")
                response = requests.get(url, headers=headers, params=querystring)
                time.sleep(3)  # Delay after each API call to prevent hitting rate limits

                if response.status_code != 200:
                    log_debug(f"[ERROR] API request failed (status {response.status_code}): {response.text}")
                    if attempt < retries - 1:
                        log_debug("Retrying API call...")
                        time.sleep(5)  # Additional delay before retrying
                    else:
                        log_debug("Max retries reached. Skipping post.")
                        return
                else:
                    break  # Successful request, exit retry loop
            except requests.exceptions.RequestException as e:
                log_debug(f"[ERROR] Request exception: {e}")
                time.sleep(5)  # Additional delay before retrying
        else:
            log_debug(f"[ERROR] Skipping post {urn} after {retries} failed attempts.")
            return

        data = response.json()
        comments = data.get("data", [])

        if not comments:
            log_debug(f"[INFO] No comments found on page {page}. Stopping further API calls.")
            break

        found_valid_email = False

        for comment in comments:
            text = comment.get("text", "")
            email_match = re.findall(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", text)
            name = comment['author'].get('name', 'Unknown')
            title = comment['author'].get('title', 'Unknown')
            linkedin_url = comment['author'].get('linkedinUrl', 'Unknown')

            log_debug(f"Processing comment: {text}")

            if email_match:
                email = email_match[0]
                log_debug(f"Extracted email: {email} from comment: {text}")
                if not check_email_exists(cursor, email):
                    log_debug(f"Email {email} not found in database. Validating...")
                    if validate_email(email):
                        log_debug(f"Email {email} passed validation. Checking country for: {linkedin_url}")
                        # country = get_country_from_linkedin_url(linkedin_url)
                        # log_debug(f"Country for {linkedin_url}: {country}")                        
                        insert_into_database(cursor, name, title, linkedin_url, email)
                    else:
                        log_debug(f"Email {email} failed validation.")
                else:
                    log_debug(f"Email {email} already exists in database. Skipping insertion.")
            else:
                log_debug("No valid email found in comment.")

        log_debug(f"[INFO] Processed {len(comments)} comments from page {page}")
        page += 1
        time.sleep(3)  # Delay after each API call to prevent hitting rate limits

def get_country_from_linkedin_url(linkedin_url):
    """Fetches country from a LinkedIn profile using the RapidAPI."""
    API_URL = "https://linkedin-data-api.p.rapidapi.com/get-profile-data-by-url"
    API_KEY = os.getenv("RAPIDAPI_KEY")

    headers = {
        "x-rapidapi-key": API_KEY,
        "x-rapidapi-host": "linkedin-api8.p.rapidapi.com"
    }

    try:
        response = requests.get(API_URL, headers=headers, params={"url": linkedin_url}, timeout=10)
        data = response.json()
        if "geo" in data and "country" in data["geo"]:
            return data["geo"]["country"]
    except Exception as e:
        log_debug(f"[ERROR] Failed to fetch country for {linkedin_url}: {e}")
    
    return "Unknown"


def main():
    reset_debug_log()
    
    print("Choose input source:")
    print("1 - Use LinkedIn post URLs (urls.txt)")
    print("2 - Use LinkedIn profile URLs (profile_urls.txt)")
    print("3 - Use LinkedIn post URLs from posts3.csv")
    print("4 - Use LinkedIn KOL Profile URLs from posts3.csv")

    choice = os.getenv("CHOICE")
    if not choice:
        choice = input("Enter your choice (1/2/3/4): ").strip()


    log_debug("Starting LinkedIn scraper.")            

    driver = initialize_driver()
    login_to_linkedin(driver)

    
    conn = connect_to_database()
    if conn is None:
        log_debug("Database connection failed. Exiting.")
        return
    cursor = conn.cursor()    

    
    api_key = os.getenv("RAPIDAPI_KEY")
    if choice == "1":
        urls = load_urls()
        for url in urls:
            profile_id = get_profile_id(url)
            log_debug(f"Processing LinkedIn page: {url} (Profile ID: {profile_id})")

            post_urns = get_post_urns(driver, url, profile_id)

            for urn in post_urns:
                fetch_all_comments(urn, api_key, cursor)
                save_processed_urn(profile_id, urn)
    
    elif choice == "2":
        all_profiles = load_profile_urls()
        for url in all_profiles:            
            profile_id = get_profile_id(url)
            post_urns = get_post_urns(driver, url, profile_id)

            for urn in post_urns:
                fetch_all_comments(urn, api_key, cursor)
                save_processed_urn(profile_id, urn)

    elif choice == "3":
        urls = load_urls_from_csv("posts3.csv")  # Update with actual filename
        log_debug(f"[DEBUG] Choice 3 selected. URLs loaded: {len(urls)}")

        if not urls:
            log_debug("No URLs found in the input file. Exiting.")
            return
    
        processed_csv_urls = load_processed_csv_urls()

        for i, url in enumerate(urls):
            if url in processed_csv_urls:
                log_debug(f"[SKIP] URL already processed: {url}")
                continue

            # Restart driver every 10 URLs to avoid memory issues
            if i > 0 and i % 10 == 0:
                log_debug(f"[INFO] Restarting driver at index {i}")
                driver.quit()
                driver = initialize_driver()
                login_to_linkedin(driver)

            profile_id = get_profile_id(url) or f"csv_post_{i}"
            log_debug(f"[INFO] Processing CSV URL: {url}")

            post_urns = get_post_urns(driver, url, profile_id)
            for urn in post_urns:
                fetch_post_comments_from_fresh_api(urn, cursor, api_key)
                save_processed_urn(profile_id, urn)

            save_processed_csv_url(url)
    
    elif choice == "4":
        profiles = load_kol_profiles_from_csv("posts3.csv")
        processed_profiles = load_processed_profiles()

        for profile_url in profiles:
            username = extract_username_from_url(profile_url)
            if not username:
                log_debug(f"[SKIP] Invalid profile URL: {profile_url}")
                continue
            if username in processed_profiles:
                log_debug(f"[SKIP] Already processed: {username}")
                continue

            log_debug(f"[INFO] Getting URN for: {username}")
            urn = get_urn_from_username(username, api_key)
            if not urn:
                log_debug(f"[SKIP] Could not retrieve URN for {username}")
                continue

            log_debug(f"[INFO] Getting posts for URN: {urn}")
            urns = get_post_ids_from_profile(urn, api_key)

            for post_urn in urns:
                fetch_post_comments_from_fresh_api(post_urn, cursor, api_key)
                save_processed_urn(username, post_urn)

            save_processed_profile(username)


    
    cursor.close()
    conn.close()    
    log_debug("Scraper finished.")

if __name__ == "__main__":
    main()