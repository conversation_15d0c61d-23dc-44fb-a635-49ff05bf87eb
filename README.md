# 🕸️ LinkedIn Email Scraper

A powerful LinkedIn post comment scraper built using Selenium, PostgreSQL, and RapidAPI. This tool extracts emails from public comments on LinkedIn posts, validates them via MillionVerifier, and stores valid emails in a PostgreSQL mailing list database.

---

## 🚀 Features

- Scrapes comments from LinkedIn posts or profiles
- Detects emails using regex
- Validates emails with MillionVerifier API
- Stores data in PostgreSQL (`mailingList` table)
- Tracks progress with processed URNs and logs
- Supports CSV input, `urls.txt`, and `profile_urls.txt`
- Robust logging and retry logic

---

## 📁 Main Project Structure ( LinkedIn Email Scraper Using API )

```bash
LINKEDIN-POST-CRAWLER/
├── mocker/
│ ├── linkedin_scraper_api.py # 🔧 Main scraper logic
│ ├── insert_to_db.py # DB insert logic
│ ├── debug_log.txt # Latest debug log
│ ├── posts.csv / posts2.csv / posts3.csv # Post URL CSVs
│ ├── profile_urls.txt # Tracked profile links
│ ├── shann5.pkl # Saved LinkedIn cookies
├── requirements.txt # Python dependencies
├── .gitattributes
```

## 🛠️ Requirements

- Python 3.8+
- Google Chrome & matching ChromeDriver
- PostgreSQL database
- Valid API keys:
  - RapidAPI (LinkedIn and Profile APIs)
  - MillionVerifier email validation API

### 🧪 Python Dependencies

Install with:

```bash
pip install -r requirements.txt
```

### ⚙️ Usage

Prepare Input:

Add post URLs to urls.txt

OR add profile URLs to profile_urls.txt

OR add a posts3.csv file with a Post column

Run the Scraper:

python scraper.py

Choose your input type:

1 - Use LinkedIn post URLs (urls.txt)
2 - Use LinkedIn profile URLs (profile_urls.txt)
3 - Use LinkedIn post URLs from posts3.csv ( the csv can be changed manually from the code )

### 🧠 How It Works

Logs into LinkedIn using cookies (shann5.pkl)

Scrolls through profile or search page to find recent posts

Collects posts with 20+ comments

Calls RapidAPI to fetch post comments

Extracts and validates emails

Saves valid, unique emails to PostgreSQL
